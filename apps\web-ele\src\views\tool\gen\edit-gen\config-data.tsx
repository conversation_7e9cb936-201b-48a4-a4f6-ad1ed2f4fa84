import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { listToTree } from '@vben/utils';

import { menuList } from '#/api/system/menu';

export const basicSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'tableName',
    label: '表名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'tableComment',
    label: '表描述',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'className',
    label: '实体类名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'functionAuthor',
    label: '作者',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'remark',
    label: '备注',
    componentProps: {
      type: 'textarea',
    },
    formItemClass: 'col-span-2',
  },
];

export const javaTypes = [
  {
    label: 'Long',
    value: 'Long',
  },
  {
    label: 'String',
    value: 'String',
  },
  {
    label: 'Integer',
    value: 'Integer',
  },
  {
    label: 'Double',
    value: 'Double',
  },
  {
    label: 'BigDecimal',
    value: 'BigDecimal',
  },
  {
    label: 'Date',
    value: 'Date',
  },
  {
    label: 'Boolean',
    value: 'Boolean',
  },
];

export const queryTypes = [
  {
    label: '=',
    value: 'EQ',
  },
  {
    label: '!=',
    value: 'NE',
  },
  {
    label: '>',
    value: 'GT',
  },
  {
    label: '>=',
    value: 'GTE',
  },
  {
    label: '<',
    value: 'LT',
  },
  {
    label: '<=',
    value: 'LTE',
  },
  {
    label: 'LIKE',
    value: 'LIKE',
  },
  {
    label: 'BETWEEN',
    value: 'BETWEEN',
  },
];

export const htmlTypes = [
  {
    label: '文本框',
    value: 'input',
  },
  {
    label: '文本域',
    value: 'textarea',
  },
  {
    label: '下拉框',
    value: 'select',
  },
  {
    label: '单选框',
    value: 'radio',
  },
  {
    label: '复选框',
    value: 'checkbox',
  },
  {
    label: '日期控件',
    value: 'datetime',
  },
  {
    label: '图片上传',
    value: 'imageUpload',
  },
  {
    label: '文件上传',
    value: 'fileUpload',
  },
  {
    label: '富文本控件',
    value: 'editor',
  },
];

export const columns: VxeGridProps['columns'] = [
  {
    type: 'seq',
    title: '序号',
    width: 60,
  },
  {
    title: '字段列名',
    field: 'columnName',
    minWidth: '10%',
  },
  {
    field: 'columnComment',
    title: '字段描述',
    slots: { default: 'columnComment' },
    minWidth: '10%',
  },
  {
    title: '物理类型',
    field: 'columnType',
    minWidth: '10%',
  },
  {
    title: 'Java类型',
    field: 'javaType',
    slots: { default: 'javaType' },
    minWidth: '11%',
  },
  {
    title: 'Java属性',
    field: 'javaField',
    slots: { default: 'javaField' },
    minWidth: '10%',
  },
  {
    title: '插入',
    field: 'isInsert',
    slots: { default: 'isInsert' },
    minWidth: '5%',
  },
  {
    title: '编辑',
    field: 'isEdit',
    slots: { default: 'isEdit' },
    minWidth: '5%',
  },
  {
    title: '列表',
    field: 'isList',
    slots: { default: 'isList' },
    minWidth: '5%',
  },
  {
    title: '查询',
    field: 'isQuery',
    slots: { default: 'isQuery' },
    minWidth: '5%',
  },
  {
    title: '查询方式',
    field: 'queryType',
    slots: { default: 'queryType' },
    minWidth: '10%',
  },
  {
    title: '必填',
    field: 'isRequired',
    slots: { default: 'isRequired' },
    minWidth: '5%',
  },
  {
    title: '显示类型',
    field: 'htmlType',
    slots: { default: 'htmlType' },
    minWidth: '12%',
  },
  {
    title: '字典类型',
    field: 'dictType',
    slots: { default: 'dictType' },
    minWidth: '12%',
  },
];

const tplCategorys = [
  {
    label: '单表（增删改查）',
    value: 'crud',
  },
  {
    label: '树表（增删改查）',
    value: 'tree',
  },
  {
    label: '主子表（增删改查）',
    value: 'sub',
  },
];
const tplWebTypes = [
  {
    label: 'Vue2 Element UI 模版',
    value: 'element-ui',
  },
  {
    label: 'Vue3 Element Plus 模版',
    value: 'element-plus',
  },
];
const genTypes = [
  {
    label: 'zip压缩包',
    value: '0',
  },
  {
    label: '自定义路径',
    value: '1',
  },
];
export const genSchema: FormSchemaGetter = () => [
  {
    component: 'Select',
    fieldName: 'tplCategory',
    componentProps: {
      options: tplCategorys,
    },
    label: '生成模板',
    rules: 'required',
  },
  {
    component: 'Select',
    fieldName: 'tplWebType',
    componentProps: {
      options: tplWebTypes,
    },
    label: '前端类型',
  },
  {
    component: 'Input',
    fieldName: 'packageName',
    label: '生成包路径',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'moduleName',
    label: '生成模块名',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'businessName',
    label: '生成业务名',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'functionName',
    label: '生成功能名',
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    fieldName: 'genType',
    componentProps: {
      isButton: true,
      options: genTypes,
    },
    label: '生成代码方式',
    rules: 'required',
  },
  {
    component: 'ApiTreeSelect',
    componentProps: {
      api: async () => {
        const menuLists = await menuList();
        return listToTree(menuLists, {
          id: 'menuId',
          pid: 'parentId',
        });
      },
      labelField: 'menuName',
      valueField: 'menuId',
      childrenField: 'children',
      checkStrictly: true,
    },
    fieldName: 'parentMenuId',
    label: '上级菜单',
  },
];
