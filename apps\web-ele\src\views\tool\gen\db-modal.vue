<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { defineEmits } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { dbList, importDb } from '#/api/tool/gen/gen';

import { modalColumns, modalQuerySchema } from './config-data';

const emit = defineEmits<{ reload: [] }>();

const [Modal, modelApi] = useVbenModal({
  centered: true,
  async onConfirm() {
    const gens = basicTableApi.grid.getCheckboxRecords();
    const tableNames = gens.map((item) => item.tableName);
    await importDb(tableNames);
    emit('reload');
    await modelApi.close();
  },
});
const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: modalQuerySchema(),
  wrapperClass: 'grid-cols-3',
  // 日期选择格式化
  fieldMappingTime: [
    [
      'createTime',
      ['params[beginTime]', 'params[endTime]'],
      ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
    ],
  ],
};

// 列表中显示配置
const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    trigger: 'default',
  },
  columns: modalColumns,
  size: 'medium',
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        const resp = await dbList({
          ...formValues,
          pageNum: page.currentPage,
          pageSize: page.pageSize,
        });
        return { items: resp.rows, total: resp.total };
      },
    },
  },
};
const [BasicTable, basicTableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
</script>

<template>
  <Modal title="导入表" class="h-[700px] w-[800px]">
    <BasicTable />
  </Modal>
</template>
