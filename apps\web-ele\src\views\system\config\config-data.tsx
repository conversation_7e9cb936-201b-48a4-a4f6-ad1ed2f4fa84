import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';

import { getDictOptions } from '#/utils/dict';

export const queryFormSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'configName',
    label: '参数名称',
  },
  {
    component: 'Input',
    fieldName: 'configKey',
    label: '参数键名',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_YES_NO),
    },
    fieldName: 'configType',
    label: '系统内置',
  },
  {
    component: 'DatePicker',
    componentProps: {
      type: 'daterange',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
    },
    fieldName: 'createTime',
    label: '创建时间',
  },
];

export const tableColumns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 80,
  },
  {
    field: 'configId',
    title: '参数主键',
  },
  {
    field: 'configName',
    title: '参数名称',
  },
  {
    field: 'configKey',
    title: '参数键名',
  },
  {
    field: 'configValue',
    title: '参数键值',
  },
  {
    field: 'configType',
    slots: { default: 'configType' },
    title: '系统内置',
  },
  {
    field: 'createTime',
    title: '创建时间',
  },
  {
    field: 'remark',
    title: '备注',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
  },
];

export const drawerFormSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'configId',
  },
  {
    component: 'Input',
    fieldName: 'configName',
    label: '参数名称',
    componentProps: {
      maxlength: 100,
    },
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'configKey',
    label: '参数键名',
    componentProps: {
      maxlength: 100,
    },
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'configValue',
    label: '参数键值',
    componentProps: {
      maxlength: 500,
      type: 'textarea',
      showWordLimit: true,
    },
  },
  {
    component: 'RadioGroup',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_YES_NO),
      isButton: true,
    },
    fieldName: 'configType',
    defaultValue: 'Y',
    label: '状态',
  },
  {
    component: 'Input',
    fieldName: 'remark',
    label: '备注',
    componentProps: {
      maxlength: 500,
      type: 'textarea',
      showWordLimit: true,
    },
  },
];
