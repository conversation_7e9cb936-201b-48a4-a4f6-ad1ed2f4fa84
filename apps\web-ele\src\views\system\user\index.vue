<script lang="ts" setup>
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { Dept } from '#/api/system/dept/dept';
import type { User } from '#/api/system/user/model';

import { onMounted, ref, watch } from 'vue';

import { useAccess } from '@vben/access';
import { ColPage, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';
import { $t } from '@vben/locales';
import { getPopupContainer } from '@vben/utils';

import { VbenAvatar } from '@vben-core/shadcn-ui';

import { Search } from '@element-plus/icons-vue';
import { ElMessageBox } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deptTree } from '#/api/system/dept/dept';
import {
  deleteUsers,
  getUserList,
  updateUserStatus,
  userExport,
} from '#/api/system/user/user';
import { getDictOptions } from '#/utils/dict';
import { commonDownloadExcel } from '#/utils/file/download';

import { columns } from './config-data';
import userDrawer from './user-drawer.vue';
import UserImport from './user-import.vue';
import UserInfoModal from './user-info.vue';
import UserPwd from './user-pwd.vue';

const treeDataRef = ref<Dept[]>([]);
const treeRef = ref();
const searchTextRef = ref<string>('');
// 左侧属性搜索关键字监听过滤
watch(searchTextRef, (val) => {
  // eslint-disable-next-line unicorn/no-array-callback-reference
  treeRef.value!.filter(val);
});

interface Tree {
  [key: string]: any;
}
// 过滤树形图回调 配置这treeRef。filter使用触发
const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.label.includes(value);
};
// 头部查询表单配置
const formOptions: VbenFormProps = {
  handleReset: onQueryRest,
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: [
    {
      component: 'Input',
      fieldName: 'userName',
      label: '用户账号',
    },
    {
      component: 'Input',
      fieldName: 'phonenumber',
      componentProps: {
        maxlength: 11,
      },
      label: '手机号码',
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer,
        options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
      },
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'DatePicker',
      componentProps: {
        type: 'daterange',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
      },
      fieldName: 'createTime',
      label: '创建时间',
      formItemClass: 'col-span-2 xl:grid-cols-2',
    },
  ],
  // 日期选择格式化
  fieldMappingTime: [
    [
      'createTime',
      ['params[beginTime]', 'params[endTime]'],
      ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
    ],
  ],
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};
async function onQueryRest() {
  // 重置左侧树状图选中的节点
  treeRef.value.setCurrentKey(null);

  const { formApi, reload } = GridApi;
  await formApi.resetForm();
  const formValues = formApi.form.values;
  formApi.setLatestSubmissionValues(formValues);
  await reload(formValues);
}
// 列表中显示配置
const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    trigger: 'default',
    checkMethod: ({ row }) => row?.userId !== 1,
  },
  columns,
  size: 'medium',
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        const resp = await getUserList({
          ...formValues,
          pageNum: page.currentPage,
          pageSize: page.pageSize,
        });
        return { items: resp.rows, total: resp.total };
      },
    },
  },
  rowConfig: {
    keyField: 'userId',
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    zoom: true,
  },
  id: 'system-user-index',
};
// 列表组件配置
const [BasicTable, GridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
// 侧拉组件连接
const [UserDrawer, userDrawerApi] = useVbenDrawer({
  connectedComponent: userDrawer,
});

// 用户信息弹窗
const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  centered: true,
  footer: false,
  // 连接抽离的组件
  connectedComponent: UserInfoModal,
});
// 用户修改密码弹窗
const [PwdModal, PwdModalApi] = useVbenModal({
  fullscreenButton: false,
  centered: true,
  // 连接抽离的组件
  connectedComponent: UserPwd,
});
// 用户修改密码弹窗
const [ImportModal, ImportModalApi] = useVbenModal({
  fullscreenButton: false,
  centered: true,
  // 连接抽离的组件
  connectedComponent: UserImport,
});

function userImport() {
  ImportModalApi.open();
}

// 打开用户信息弹窗
function openModal(row: User) {
  modalApi.setData(row).open();
}
// 修改密码弹窗显示
function openPwdModal(row: User) {
  PwdModalApi.setData(row).open();
}
// 添加用户
function handleAdd() {
  userDrawerApi.setData({});
  userDrawerApi.open();
}
// 编辑用户
function handleEdit(row: User) {
  userDrawerApi.setData({ id: row.userId, update: true });
  userDrawerApi.open();
}
// 导出用户
async function userExports() {
  await commonDownloadExcel(
    userExport,
    '用户管理',
    GridApi.formApi.form.values,
    {
      fieldMappingTime: formOptions.fieldMappingTime,
    },
  );
}
// 点击左侧树形图某一个节点
function nodeClick(currentNode: Dept) {
  GridApi.reload({ deptId: currentNode.id });
}
// 操作表格上用户状态开关触发
function userStatusChange(row: User) {
  ElMessageBox.confirm(
    `确定要${row.status === '0' ? '启用' : '停用'}${row.nickName}?`,
    '系统提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(async () => {
      await updateUserStatus(row);
    })
    .catch(() => {
      row.status = row.status === '0' ? '1' : '0';
    });
}
// 多选删除用户
function handleDeletes() {
  // 获取全部选中的数据
  const checkRecords = GridApi.grid.getCheckboxRecords();
  // 数据转换只要userId
  const ids = checkRecords.map((item: User) => item.userId);
  ElMessageBox.confirm(`确认删除选中的${ids.length}条数据吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    // 删除用户
    await deleteUsers(ids);
    // 重置查询
    await GridApi.reload();
  });
}
// 单个删除用户
async function confirmEvent(row: User) {
  await deleteUsers([row.userId]);
  await GridApi.reload();
}
const { hasAccessByCodes } = useAccess();
onMounted(async () => {
  // 页面挂载获取部门树形图
  treeDataRef.value = await deptTree();
});
</script>
<template>
  <div>
    <ColPage :left-width="20" auto-content-height>
      <template #left>
        <div
          :style="{ minWidth: '200px' }"
          class="border-border bg-card mr-2 h-full rounded-[var(--radius)] border p-2"
        >
          <ElInput
            v-model="searchTextRef"
            placeholder="请输入部门名称"
            :prefix-icon="Search"
          />
          <ElTree
            ref="treeRef"
            :data="treeDataRef"
            node-key="id"
            default-expand-all
            highlight-current
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            @node-click="nodeClick"
            class="mt-2"
          />
        </div>
      </template>
      <div
        class="border-border bg-card mr-2 h-full rounded-[var(--radius)] border p-2"
      >
        <BasicTable class="flex-1 overflow-hidden" table-title="用户列表">
          <template #avatar="{ row }">
            <div class="flex w-full justify-center">
              <VbenAvatar
                :src="row.avatar"
                :size="30"
                :alt="row.avatar ? row.avatar : '暂无'"
              />
            </div>
          </template>
          <template #status="{ row }">
            <ElSwitch
              v-model="row.status"
              inline-prompt
              active-text="正常"
              inactive-text="停用"
              active-value="0"
              inactive-value="1"
              style="

--el-switch-off-color: #ff4949"
              @change="userStatusChange(row)"
              :disabled="!hasAccessByCodes(['system:user:edit'])"
            />
          </template>
          <template #toolbar-tools>
            <ElSpace>
              <ElButton
                @click="userExports"
                v-access:code="['system:user:export']"
              >
                导出
              </ElButton>
              <ElButton
                @click="userImport"
                v-access:code="['system:user:import']"
              >
                导入
              </ElButton>
              <ElButton
                type="danger"
                :disabled="!(GridApi?.grid?.getCheckboxRecords?.()?.length > 0)"
                @click="handleDeletes"
                v-access:code="['system:user:remove']"
              >
                删除
              </ElButton>
              <ElButton
                type="primary"
                @click="handleAdd"
                v-access:code="['system:user:add']"
              >
                {{ $t('pages.common.add') }}
              </ElButton>
            </ElSpace>
          </template>
          <template #action="{ row }">
            <ElSpace v-if="row.userId !== 1">
              <ElButton
                size="small"
                type="primary"
                plain
                @click="handleEdit(row)"
                v-access:code="['system:user:edit']"
              >
                编辑
              </ElButton>
              <ElPopconfirm title="确认删除" @confirm="confirmEvent(row)">
                <template #reference>
                  <ElButton
                    size="small"
                    type="danger"
                    plain
                    v-access:code="['system:user:remove']"
                  >
                    删除
                  </ElButton>
                </template>
              </ElPopconfirm>
              <ElDropdown>
                <ElButton
                  type="primary"
                  size="small"
                  link
                  v-access:code="['system:user:query', 'system:user:resetPwd']"
                >
                  更多
                </ElButton>
                <template #dropdown>
                  <ElDropdownMenu>
                    <span v-access:code="['system:user:query']">
                      <ElDropdownItem @click="openModal(row)">
                        用户信息
                      </ElDropdownItem>
                    </span>
                    <span v-access:code="['system:user:resetPwd']">
                      <ElDropdownItem @click="openPwdModal(row)">
                        重置密码
                      </ElDropdownItem>
                    </span>
                  </ElDropdownMenu>
                </template>
              </ElDropdown>
            </ElSpace>
          </template>
        </BasicTable>
      </div>
    </ColPage>
    <UserDrawer @reload="GridApi.query()" />
    <Modal />
    <PwdModal @reload="GridApi.query()" />
    <ImportModal @reload="GridApi.query()" />
  </div>
</template>
<style scoped lang="scss">
:deep(.el-tooltip__trigger:focus) {
  outline: none;
}
</style>
