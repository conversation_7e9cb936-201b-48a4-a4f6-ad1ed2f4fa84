import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';

import { getDictOptions } from '#/utils/dict';

export const tableColumns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    field: 'infoId',
    title: '访问编号',
  },
  {
    field: 'userName',
    title: '用户账号',
  },
  {
    field: 'ipaddr',
    title: '登录地址',
  },
  {
    field: 'loginLocation',
    title: '登录地点',
  },
  {
    field: 'browser',
    title: '浏览器',
  },
  {
    field: 'os',
    title: '操作系统',
  },
  {
    field: 'status',
    title: '操作状态',
    slots: { default: 'status' },
  },
  {
    field: 'msg',
    title: '操作信息',
  },
  {
    field: 'loginTime',
    title: '登录时间',
    minWidth: 80,
  },
];

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    label: '登录地址',
    fieldName: 'ipaddr',
  },
  {
    component: 'Input',
    label: '用户名称',
    fieldName: 'userName',
  },
  {
    component: 'Select',
    label: '操作状态',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_COMMON_STATUS),
    },
    fieldName: 'status',
  },
  {
    component: 'DatePicker',
    componentProps: {
      type: 'daterange',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
    },
    fieldName: 'operTime',
    label: '登录日期',
  },
];
