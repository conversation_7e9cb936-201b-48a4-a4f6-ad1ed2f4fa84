import type { JobLog } from './model';

import type { BaseResult } from '#/api/base-result';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  baseApi = '/monitor/jobLog',
  cleanJobLog = '/monitor/jobLog/clean',
  exportJobLog = '/monitor/jobLog/export',
  jobLogList = '/monitor/jobLog/list',
}

// 获取列表
export function listJobLog(params: any) {
  return requestClient.get<BaseResult<JobLog[]>>(Api.jobLogList, {
    params,
  });
}
// 获取详情
export function jobLogDetail(jobLogId: number) {
  return requestClient.get<JobLog>(`${Api.baseApi}/${jobLogId}`);
}
// 删除列表
export function deleteJobLog(jobLogIds: number[]) {
  return requestClient.delete(`${Api.baseApi}/${jobLogIds.join(',')}`, {
    successMessageMode: 'message',
  });
}
// 清空列表
export function cleanJobLog() {
  return requestClient.delete(Api.cleanJobLog, {
    successMessageMode: 'message',
  });
}
// 导出
export function exportJobLog(data: Partial<JobLog>) {
  return commonExport(Api.exportJobLog, data);
}
