import type {
  Deployment,
  ExportQuery,
  FlowTaskFormQuery,
  NextFlowNodeByStartData,
  NextFlowNodeData,
  ReturnListData,
  TaskCompleteData,
  TaskDelegateData,
  TaskRejectData,
  TaskReturnData,
  TodoQuery,
} from './model';

import type { BaseResult } from '#/api/base-result';

import { requestClient } from '#/api/request';

enum Api {
  todoList = '/flowable/task/todoList',
  complete = '/flowable/task/complete',
  delegate = '/flowable/task/delegate',
  returnTask = '/flowable/task/return',
  reject = '/flowable/task/reject',
  returnList = '/flowable/task/returnList',
  nextFlowNode = '/flowable/task/nextFlowNode',
  nextFlowNodeByStart = '/flowable/task/nextFlowNodeByStart',
  startFlow = '/flowable/process/startFlow',
  deployment = '/system/deployment',
  exportDeployment = '/system/deployment/export',
  flowTaskForm = '/flowable/task/flowTaskForm',
}

// 查询待办任务列表
export function todoList(params?: TodoQuery) {
  return requestClient.get<BaseResult<any[]>>(Api.todoList, { params });
}

// 完成任务
export function complete(data: TaskCompleteData) {
  return requestClient.post<BaseResult<any>>(Api.complete, data, { successMessageMode: 'message' });
}

// 委派任务
export function delegate(data: TaskDelegateData) {
  return requestClient.post<BaseResult<any>>(Api.delegate, data, { successMessageMode: 'message' });
}

// 退回任务
export function returnTask(data: TaskReturnData) {
  return requestClient.post<BaseResult<any>>(Api.returnTask, data, { successMessageMode: 'message' });
}

// 驳回任务
export function rejectTask(data: TaskRejectData) {
  return requestClient.post<BaseResult<any>>(Api.reject, data, { successMessageMode: 'message' });
}

// 可退回任务列表
export function returnList(data: ReturnListData) {
  return requestClient.post<BaseResult<any[]>>(Api.returnList, data);
}

// 下一节点
export function getNextFlowNode(data: NextFlowNodeData) {
  return requestClient.post<BaseResult<any>>(Api.nextFlowNode, data);
}

// 下一节点(启动时)
export function getNextFlowNodeByStart(data: NextFlowNodeByStartData) {
  return requestClient.post<BaseResult<any>>(Api.nextFlowNodeByStart, data);
}

// 部署流程实例
export function deployStart(deployId: string) {
  return requestClient.get<BaseResult<any>>(`${Api.startFlow}/${deployId}`);
}

// 查询流程定义详细
export function getDeployment(id: string) {
  return requestClient.get<BaseResult<Deployment>>(`${Api.deployment}/${id}`);
}

// 新增流程定义
export function addDeployment(data: Deployment) {
  return requestClient.post<BaseResult<any>>(Api.deployment, data, { successMessageMode: 'message' });
}

// 修改流程定义
export function updateDeployment(data: Deployment) {
  return requestClient.put<BaseResult<any>>(Api.deployment, data, { successMessageMode: 'message' });
}

// 删除流程定义
export function delDeployment(id: string) {
  return requestClient.delete<BaseResult<any>>(`${Api.deployment}/${id}`, { successMessageMode: 'message' });
}

// 导出流程定义
export function exportDeployment(params?: ExportQuery) {
  return requestClient.get<BaseResult<any>>(Api.exportDeployment, { params });
}

// 流程节点表单
export function flowTaskForm(params?: FlowTaskFormQuery) {
  return requestClient.get<BaseResult<any>>(Api.flowTaskForm, { params });
}