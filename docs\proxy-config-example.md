# 生产环境代理配置示例

## 问题说明

当外部网站设置了 `X-Frame-Options: sameorigin` 时，会阻止跨域 iframe 嵌入。本项目通过代理的方式解决此问题。

## 开发环境

已在 `apps/web-ele/vite.config.mts` 中配置了 Vite 代理：

```typescript
'/proxy-geovis': {
  changeOrigin: true,
  rewrite: (path) => path.replace(/^\/proxy-geovis/, ''),
  target: 'https://cyyj.geovisearth.com',
  ws: false,
  configure: (proxy, _options) => {
    proxy.on('proxyRes', (proxyRes, req, res) => {
      // 移除 X-Frame-Options 头以允许 iframe 嵌入
      delete proxyRes.headers['x-frame-options'];
      delete proxyRes.headers['X-Frame-Options'];
      // 移除 CSP 头以避免内容安全策略冲突
      delete proxyRes.headers['content-security-policy'];
      delete proxyRes.headers['Content-Security-Policy'];
    });
  },
},
```

## 生产环境配置

### Nginx 配置示例

```nginx
# 在您的 Nginx 配置中添加以下 location 块
location /proxy/geovis/ {
    # 移除前缀并代理到目标服务器
    rewrite ^/proxy/geovis/(.*)$ /$1 break;
    proxy_pass https://cyyj.geovisearth.com;
    
    # 基本代理设置
    proxy_set_header Host cyyj.geovisearth.com;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 移除可能导致 iframe 嵌入问题的响应头
    proxy_hide_header X-Frame-Options;
    proxy_hide_header Content-Security-Policy;
    
    # 添加允许 iframe 嵌入的头
    add_header X-Frame-Options "ALLOWALL" always;
    
    # 处理跨域
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range" always;
    
    # 处理预检请求
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range";
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type "text/plain; charset=utf-8";
        add_header Content-Length 0;
        return 204;
    }
}
```

### Spring Boot 配置示例

如果您使用 Spring Boot 作为后端，可以添加以下配置：

```java
@RestController
@RequestMapping("/proxy/geovis")
public class ProxyController {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @GetMapping("/**")
    public ResponseEntity<String> proxyGet(HttpServletRequest request, HttpServletResponse response) {
        String path = request.getRequestURI().replace("/proxy/geovis", "");
        String targetUrl = "https://cyyj.geovisearth.com" + path;
        
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", request.getHeader("User-Agent"));
            
            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                targetUrl, HttpMethod.GET, entity, String.class);
            
            // 移除可能导致问题的响应头
            HttpHeaders responseHeaders = new HttpHeaders();
            responseEntity.getHeaders().forEach((key, value) -> {
                if (!key.equalsIgnoreCase("X-Frame-Options") && 
                    !key.equalsIgnoreCase("Content-Security-Policy")) {
                    responseHeaders.put(key, value);
                }
            });
            
            // 添加允许 iframe 嵌入的头
            responseHeaders.set("X-Frame-Options", "ALLOWALL");
            
            return new ResponseEntity<>(responseEntity.getBody(), responseHeaders, responseEntity.getStatusCode());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("代理请求失败: " + e.getMessage());
        }
    }
}
```

## 使用说明

1. **开发环境**: 直接启动项目即可，Vite 会自动处理代理
2. **生产环境**: 需要在您的 Web 服务器（如 Nginx）或后端服务中配置相应的代理规则

## 注意事项

1. 确保目标网站允许被代理访问
2. 注意处理认证和会话状态
3. 监控代理的性能和稳定性
4. 遵守目标网站的使用条款和法律法规
