import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';

import { getDictOptions } from '#/utils/dict';

const misfirePolicyArr = [
  {
    label: '立即执行',
    value: '1',
  },
  {
    label: '执行一次',
    value: '2',
  },
  {
    label: '放弃执行',
    value: '3',
  },
];

const concurrentArr = [
  {
    label: '允许',
    value: '0',
  },
  {
    label: '禁止',
    value: '1',
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'jobId',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    component: 'Input',
    fieldName: 'jobName',
    componentProps: {
      maxlength: 64,
    },
    label: '任务名称',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_JOB_GROUP),
    },
    fieldName: 'jobGroup',
    defaultValue: 'DEFAULT',
    label: '任务分组',
  },
  {
    component: 'Input',
    fieldName: 'invokeTarget',
    componentProps: {
      maxlength: 500,
    },
    label: '调用方法',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'cronExpression',
    componentProps: {
      maxlength: 255,
    },
    label: 'cron表达式',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_JOB_STATUS),
    },
    fieldName: 'status',
    defaultValue: '0',
    label: '状态',
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      options: misfirePolicyArr,
      isButton: true,
    },
    fieldName: 'misfirePolicy',
    defaultValue: '1',
    label: '执行策略',
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      options: concurrentArr,
      isButton: true,
    },
    fieldName: 'concurrent',
    defaultValue: '0',
    label: '是否并发',
    rules: 'required',
  },
];

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'jobName',
    label: '任务名称',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_JOB_GROUP),
    },
    fieldName: 'jobGroup',
    label: '任务组名',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_JOB_STATUS),
    },
    fieldName: 'status',
    label: '任务状态',
  },
];

export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    field: 'jobId',
    title: '任务编号',
  },
  {
    field: 'jobName',
    title: '任务名称',
  },
  {
    field: 'jobGroup',
    title: '任务组名',
    slots: { default: 'jobGroup' },
  },
  {
    title: '调用目标字符串',
    field: 'invokeTarget',
  },
  {
    title: 'cron执行表达式',
    field: 'cronExpression',
  },
  {
    field: 'status',
    title: '状态',
    slots: { default: 'status' },
  },
  {
    title: '创建时间',
    field: 'createTime',
    minWidth: 80,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    resizable: false,
    title: '操作',
    width: 180,
  },
];
