// 部署信息
export interface Deployment {
  id?: string;
  name?: string;
  category?: string;
  key?: string;
  tenantId?: string;
  deploymentTime?: string;
}

// 导出查询参数
export interface ExportQuery {
  deploymentId?: string;
  resourceName?: string;
}

// 待办任务查询参数
export interface TodoQuery {
  pageNum?: number;
  pageSize?: number;
  taskName?: string;
  category?: string;
  key?: string;
}

// 任务完成数据
export interface TaskCompleteData {
  taskId: string;
  procInsId?: string;
  comment?: string;
  variables?: Record<string, any>;
  targetUserIds?: string[];
  targetRoleIds?: string[];
  [key: string]: any;
}

// 任务委派数据
export interface TaskDelegateData {
  taskId: string;
  userId: string;
  comment?: string;
}

// 任务退回数据
export interface TaskReturnData {
  taskId: string;
  procInsId?: string;
  comment?: string;
  targetKey?: string;
}

// 任务驳回数据
export interface TaskRejectData {
  taskId: string;
  procInsId?: string;
  comment?: string;
  targetKey?: string;
}

// 退回任务列表数据
export interface ReturnListData {
  taskId: string;
  procInsId?: string;
}

// 下一节点数据
export interface NextFlowNodeData {
  taskId: string;
  variables?: Record<string, any>;
}

// 下一节点(启动时)数据
export interface NextFlowNodeByStartData {
  procDefId: string;
  variables?: Record<string, any>;
}

// 流程任务表单查询参数
export interface FlowTaskFormQuery {
  taskId?: string;
  procInsId?: string;
  procDefId?: string;
}