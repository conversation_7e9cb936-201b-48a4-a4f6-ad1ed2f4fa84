import type { <PERSON>u, <PERSON>uOption, <PERSON>u<PERSON><PERSON>y, <PERSON>uResp } from './model';

import type { ID } from '#/api/common';

import { requestClient } from '#/api/request';

enum Api {
  menuList = '/system/menu/list',
  menuTreeList = '/system/menu/treeselect',
  roleMenuTree = '/system/menu/roleMenuTreeselect',
  root = '/system/menu',
}
/**
 * 菜单列表
 * @param params 参数
 * @returns 列表
 */
export function menuList(params?: MenuQuery) {
  return requestClient.get<Menu[]>(Api.menuList, { params });
}
/**
 * 菜单详情
 * @param menuId 菜单id
 * @returns 菜单详情
 */
export function menuInfo(menuId: ID) {
  return requestClient.get<Menu>(`${Api.root}/${menuId}`);
}

/**
 * 删除菜单
 * @param menuId 菜单id
 * @returns 删除结果
 */
export function menuDel(menuId: ID) {
  return requestClient.delete<Menu>(`${Api.root}/${menuId}`, {
    successMessageMode: 'message',
  });
}

/**
 * 菜单新增
 * @param data 参数
 */
export function menuAdd(data: Partial<Menu>) {
  return requestClient.post(Api.root, data);
}

/**
 * 菜单更新
 * @param data 参数
 */
export function menuUpdate(data: Partial<Menu>) {
  return requestClient.put(Api.root, data);
}
/**
 * 新增角色侧拉菜单中的属性菜单需要用到
 * @param params 参数
 * @returns 列表
 */
export function menuTreeList(params?: MenuQuery) {
  return requestClient.get<MenuOption[]>(Api.menuTreeList, { params });
}
/**
 * 返回对应角色的菜单
 * @param roleId id
 * @returns resp
 */
export function roleMenuTreeSelect(roleId: ID) {
  return requestClient.get<MenuResp>(`${Api.roleMenuTree}/${roleId}`);
}
