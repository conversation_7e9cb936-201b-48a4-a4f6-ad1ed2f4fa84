/**
 * iframe 代理工具函数
 * 用于处理 X-Frame-Options 限制的外部网站嵌入
 */

/**
 * 代理配置接口
 */
export interface ProxyConfig {
  /** 代理路径前缀 */
  proxyPath: string;
  /** 目标域名 */
  targetDomain: string;
  /** 生产环境代理路径 */
  prodProxyPath?: string;
}

/**
 * 生成 iframe 源地址
 * @param config 代理配置
 * @param path 目标路径
 * @param isDev 是否为开发环境
 * @param apiURL 生产环境 API 地址
 * @returns iframe 源地址
 */
export function generateIframeSrc(
  config: ProxyConfig,
  path: string,
  isDev: boolean,
  apiURL?: string,
): string {
  if (isDev) {
    // 开发环境使用 Vite 代理
    return `${config.proxyPath}${path}`;
  } else {
    // 生产环境使用后端代理
    const prodPath = config.prodProxyPath || config.proxyPath.replace('/proxy-', '/proxy/');
    return `${apiURL}${prodPath}${path}`;
  }
}

/**
 * 生成 Vite 代理配置
 * @param config 代理配置
 * @returns Vite 代理配置对象
 */
export function generateViteProxyConfig(config: ProxyConfig) {
  return {
    [config.proxyPath]: {
      changeOrigin: true,
      rewrite: (path: string) => path.replace(new RegExp(`^${config.proxyPath}`), ''),
      target: config.targetDomain,
      ws: false,
      // 移除可能导致问题的响应头
      configure: (proxy: any, _options: any) => {
        proxy.on('proxyRes', (proxyRes: any, req: any, res: any) => {
          // 移除 X-Frame-Options 头以允许 iframe 嵌入
          delete proxyRes.headers['x-frame-options'];
          delete proxyRes.headers['X-Frame-Options'];
          // 移除 CSP 头以避免内容安全策略冲突
          delete proxyRes.headers['content-security-policy'];
          delete proxyRes.headers['Content-Security-Policy'];
          // 移除其他可能导致问题的安全头
          delete proxyRes.headers['x-content-type-options'];
          delete proxyRes.headers['X-Content-Type-Options'];
        });
      },
    },
  };
}

/**
 * 预定义的常用代理配置
 */
export const COMMON_PROXY_CONFIGS = {
  /** GeoVis 地球可视化平台 */
  geovis: {
    proxyPath: '/proxy-geovis',
    targetDomain: 'https://cyyj.geovisearth.com',
    prodProxyPath: '/proxy/geovis',
  } as ProxyConfig,

  /** Vue 官方文档 */
  vueDocs: {
    proxyPath: '/proxy-vue',
    targetDomain: 'https://cn.vuejs.org',
    prodProxyPath: '/proxy/vue',
  } as ProxyConfig,

  /** Tailwind CSS 官方文档 */
  tailwindDocs: {
    proxyPath: '/proxy-tailwind',
    targetDomain: 'https://tailwindcss.com',
    prodProxyPath: '/proxy/tailwind',
  } as ProxyConfig,
};

/**
 * 生成多个代理配置的 Vite 配置
 * @param configs 代理配置数组
 * @returns 合并后的 Vite 代理配置
 */
export function generateMultipleViteProxyConfigs(configs: ProxyConfig[]) {
  return configs.reduce((acc, config) => {
    return {
      ...acc,
      ...generateViteProxyConfig(config),
    };
  }, {});
}

/**
 * 创建 iframe 代理组件的 composable
 * @param config 代理配置
 * @param path 目标路径
 * @param isDev 是否为开发环境
 * @param apiURL API 地址（可选）
 * @returns iframe 源地址的响应式引用
 */
export function useIframeProxy(config: ProxyConfig, path: string, isDev: boolean, apiURL?: string) {
  const iframeSrc = generateIframeSrc(config, path, isDev, apiURL);

  return {
    iframeSrc,
  };
}
