<script lang="ts" setup>
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { Menu } from '#/api/system/menu/model';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { menuDel, menuList } from '#/api/system/menu';

import menuDrawer from './menu-drawer.vue';
import { columns, querySchema } from './table-data';

/**
 * 不要问为什么有两个根节点 v-if会控制只会渲染一个
 */

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  columns,
  size: 'medium',
  height: 'auto',
  keepSource: true,
  proxyConfig: {
    ajax: {
      query: async (_, formValues = {}) => {
        const resp = await menuList({
          ...formValues,
        });
        return { items: resp };
      },
    },
  },
  rowConfig: {
    keyField: 'menuId',
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    zoom: true,
  },
  treeConfig: {
    parentField: 'parentId',
    rowField: 'menuId',
    transform: true,
  },
  id: 'system-menu-index',
};

const [BasicTable, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [MenuDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: menuDrawer,
});

const expandAll = () => {
  gridApi.grid?.setAllTreeExpand(true);
};

const collapseAll = () => {
  gridApi.grid?.setAllTreeExpand(false);
};
const addMenu = () => {
  drawerApi.setData({});
  drawerApi.open();
};
function handleEditMenu(record: Menu) {
  drawerApi.setData({ id: record.menuId, update: true });
  drawerApi.open();
}
function handleAddMenu(record: Menu) {
  drawerApi.setData({ id: record.menuId, update: false });
  drawerApi.open();
}
async function confirmEvent(row: Menu) {
  const result = await menuDel(row.menuId);
  console.error(result);
  await gridApi.reload();
}
</script>

<template>
  <Page class="h-full">
    <BasicTable :table-title="$t('menus.table-title')">
      <template #toolbar-tools>
        <ElSpace>
          <ElButton @click="collapseAll">
            {{ $t('pages.common.collapse') }}
          </ElButton>
          <ElButton @click="expandAll">
            {{ $t('pages.common.expand') }}
          </ElButton>
          <ElButton
            type="primary"
            @click="addMenu"
            v-access:code="['system:menu:add']"
          >
            {{ $t('pages.common.add') }}
          </ElButton>
        </ElSpace>
      </template>
      <template #action="{ row }">
        <ElSpace>
          <ElButton
            size="small"
            type="primary"
            plain
            @click="handleEditMenu(row)"
            v-access:code="['system:menu:edit']"
          >
            {{ $t('pages.common.edit') }}
          </ElButton>
          <ElButton
            v-if="row.menuType !== 'F'"
            size="small"
            type="success"
            plain
            @click="handleAddMenu(row)"
            v-access:code="['system:menu:add']"
          >
            {{ $t('pages.common.add') }}
          </ElButton>
          <ElPopconfirm
            :title="$t('common.confirm-delete')"
            @confirm="confirmEvent(row)"
          >
            <template #reference>
              <ElButton
                size="small"
                type="danger"
                plain
                v-access:code="['system:menu:remove']"
              >
                {{ $t('pages.common.delete') }}
              </ElButton>
            </template>
          </ElPopconfirm>
        </ElSpace>
      </template>
    </BasicTable>
    <MenuDrawer @reload="gridApi.query()" />
  </Page>
</template>
