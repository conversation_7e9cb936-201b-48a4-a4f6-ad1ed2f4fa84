import type { FormQuery } from './model';

import type { BaseResult } from '#/api/base-result';

import { requestClient } from '#/api/request';

// 表单信息
export interface Form {
  formId?: string;
  formName?: string;
  formKey?: string;
  formType?: string;
  formContent?: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
}

// 挂载表单数据
export interface DeployFormData {
  formKey: string;
  formName: string;
  category?: string;
  deployId?: string;
}

enum Api {
  form = '/flowable/form',
  formList = '/flowable/form/list',
  allFormList = '/flowable/form/formList',
  addDeployForm = '/flowable/form/addDeployForm',
  exportForm = '/flowable/form/export',
}

// 查询流程表单列表
export function listForm(params?: FormQuery) {
  return requestClient.get<BaseResult<Form[]>>(Api.formList, { params });
}

// 查询所有流程表单列表
export function listAllForm(params?: FormQuery) {
  return requestClient.get<BaseResult<Form[]>>(Api.allFormList, { params });
}

// 查询流程表单详细
export function getForm(formId: string) {
  return requestClient.get<BaseResult<Form>>(`${Api.form}/${formId}`);
}

// 新增流程表单
export function addForm(data: Form) {
  return requestClient.post<BaseResult<any>>(Api.form, data, { successMessageMode: 'message' });
}

// 修改流程表单
export function updateForm(data: Form) {
  return requestClient.put<BaseResult<any>>(Api.form, data, { successMessageMode: 'message' });
}

// 挂载表单
export function addDeployForm(data: DeployFormData) {
  return requestClient.post<BaseResult<any>>(Api.addDeployForm, data, { successMessageMode: 'message' });
}

// 删除流程表单
export function delForm(formId: string) {
  return requestClient.delete<BaseResult<any>>(`${Api.form}/${formId}`, { successMessageMode: 'message' });
}

// 导出流程表单
export function exportForm(params?: { formIds?: string[] }) {
  return requestClient.get<BaseResult<any>>(Api.exportForm, { params });
}