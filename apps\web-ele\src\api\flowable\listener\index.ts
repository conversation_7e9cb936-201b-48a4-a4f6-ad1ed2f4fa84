import type { ListenerQuery } from './model';

import type { BaseResult } from '#/api/base-result';

import { requestClient } from '#/api/request';

// 监听器信息
export interface Listener {
  id?: string;
  name?: string;
  type?: string;
  eventType?: string;
  valueType?: string;
  value?: string;
  status?: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
}

enum Api {
  listener = '/system/listener',
  listenerList = '/system/listener/list',
}

// 查询流程监听器列表
export function listListener(params?: ListenerQuery) {
  return requestClient.get<BaseResult<Listener[]>>(Api.listenerList, {
    params,
  });
}

// 查询流程监听器详细
export function getListener(id: string) {
  return requestClient.get<BaseResult<Listener>>(`${Api.listener}/${id}`);
}

// 新增流程监听器
export function addListener(data: Listener) {
  return requestClient.post<BaseResult<any>>(Api.listener, data, { successMessageMode: 'message' });
}

// 修改流程监听器
export function updateListener(data: Listener) {
  return requestClient.put<BaseResult<any>>(Api.listener, data, { successMessageMode: 'message' });
}

// 删除流程监听器
export function delListener(id: string) {
  return requestClient.delete<BaseResult<any>>(`${Api.listener}/${id}`, { successMessageMode: 'message' });
}