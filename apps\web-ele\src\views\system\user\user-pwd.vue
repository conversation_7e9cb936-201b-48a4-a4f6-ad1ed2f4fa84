<script lang="ts" setup>
import type { User } from '#/api/system/user/model';

import { reactive } from 'vue';

import { useVbenModal, z } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { modifyUserPwd } from '#/api/system/user/user';

const emit = defineEmits<{ reload: [] }>();
// 获取选中要修改密码的用户数据
let userReact = reactive<User>({ userId: -1, status: '0' });
const [Modal, modalApi] = useVbenModal({
  class: 'w-[430px]',
  contentClass: 'min-h-10',
  // 窗口打开赋值修改密码的用户数据
  onOpenChange() {
    userReact = modalApi.getData<User>();
  },
  async onConfirm() {
    // 表单校验
    const { valid } = await pwdFormApi.validate();
    if (!valid) {
      return;
    }
    const formUser = await pwdFormApi.getValues<User>();
    userReact.password = formUser.password;
    await modifyUserPwd(userReact);
    // 发射数据通知父组件列表刷新
    emit('reload');
    await modalApi.close();
  },
});
const [BaseForm, pwdFormApi] = useVbenForm({
  // 不显示表单校验信息
  // compact: true,
  commonConfig: {
    // 隐藏label
    hideLabel: true,
    componentProps: {
      class: 'w-full',
    },
  },
  // 不显示提交和重置按钮
  showDefaultActions: false,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入密码',
        maxlength: 20,
      },
      // 字段名
      fieldName: 'password',
      // 界面显示的label
      label: '',
      rules: z
        .string()
        .refine((val) => val.trim() !== '', {
          message: '请输入用户密码',
        })
        .refine((val) => val.length >= 6 && val.length <= 30, {
          message: '长度需在6-20字符之间',
        }),
    },
  ],
});
</script>
<template>
  <Modal title="修改密码">
    <span class="mb-4 block">请输入账号“{{ userReact.userName }}”的新密码</span>
    <BaseForm />
  </Modal>
</template>
