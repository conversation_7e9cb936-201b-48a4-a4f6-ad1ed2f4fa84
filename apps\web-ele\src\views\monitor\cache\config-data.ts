import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const cacheColumns: VxeGridProps['columns'] = [
  {
    type: 'seq',
    title: '序号',
    width: 60,
  },
  {
    field: 'cacheName',
    title: '缓存名称',
    formatter: ({ cellValue }) => {
      return cellValue.slice(0, Math.max(0, cellValue.lastIndexOf(':')));
    },
  },
  {
    field: 'remark',
    title: '备注',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
  },
];

export const keyColumns: VxeGridProps['columns'] = [
  {
    type: 'seq',
    title: '序号',
    width: 60,
  },
  {
    title: '缓存键名',
    field: 'value',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
  },
];

export const formSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    label: '缓存名称',
    fieldName: 'cacheName',
  },
  {
    component: 'Input',
    label: '缓存键名',
    fieldName: 'cacheKey',
  },
  {
    component: 'Input',
    label: '缓存内容',
    componentProps: {
      type: 'textarea',
    },
    fieldName: 'cacheValue',
  },
];
