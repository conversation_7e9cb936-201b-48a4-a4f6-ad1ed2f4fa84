import type { Operlog } from './model';

import type { BaseResult } from '#/api/base-result';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  baseApi = '/monitor/operlog',
  cleanOperlog = '/monitor/operlog/clean',
  exportOperlog = '/monitor/operlog/export',
  listOperLog = '/monitor/operlog/list',
}
// 获取操作日志列表
export function operlogList(params: any) {
  return requestClient.get<BaseResult<Operlog[]>>(Api.listOperLog, {
    params,
  });
}
// 获取操作日志详情
export function operlogDetail(operId: number) {
  return requestClient.get<Operlog>(`${Api.baseApi}/${operId}`);
}
// 删除操作日志列表
export function deleteOperlog(operIds: number[]) {
  return requestClient.delete(`${Api.baseApi}/${operIds.join(',')}`, {
    successMessageMode: 'message',
  });
}
// 清空操作日志
export function cleanOperlog() {
  return requestClient.delete(Api.cleanOperlog, {
    successMessageMode: 'message',
  });
}
// 导出
export function exportOperlog(data: Partial<Operlog>) {
  return commonExport(Api.exportOperlog, data);
}
