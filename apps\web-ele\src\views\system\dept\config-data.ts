import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { z } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';

import { deptTree } from '#/api/system/dept/dept';
import { getDictOptions } from '#/utils/dict';

export const queryFormSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'deptName',
    label: '部门名称',
  },
  {
    component: 'ApiSelect',
    fieldName: 'status',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
    },
    label: '状态',
  },
];

export const tableColumns: VxeGridProps['columns'] = [
  {
    field: 'deptName',
    title: '部门名称',
    treeNode: true,
  },
  {
    field: 'orderNum',
    title: '排序',
  },
  {
    field: 'status',
    slots: { default: 'status' },
    title: '状态',
  },
  {
    field: 'createTime',
    title: '创建时间',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    resizable: false,
    title: '操作',
    width: 180,
  },
];

// 手机号正则
const mobileRegex = /^1[3-9]\d{9}$/;
// 基础版邮箱正则
const emailRegex = /^[\w.%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i;
export const drawerFormSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      if: () => false,
      triggerFields: [''],
    },
    fieldName: 'isUpdate',
  },
  {
    component: 'Input',
    dependencies: {
      if: () => false,
      triggerFields: [''],
    },
    fieldName: 'deptId',
  },
  {
    component: 'ApiTreeSelect',
    componentProps: {
      api: async () => {
        return await deptTree();
      },
      labelField: 'label',
      valueField: 'id',
      childrenField: 'children',
      checkStrictly: true,
    },
    dependencies: {
      if(values) {
        return values.parentId !== 0 || !values.isUpdate;
      },
      triggerFields: ['parentId'],
    },
    fieldName: 'parentId',
    label: '上级部门',
    rules: 'selectRequired',
  },
  {
    component: 'Input',
    fieldName: 'deptName',
    componentProps: {
      maxlength: 30,
    },
    label: '部门名称',
    rules: 'required',
  },
  {
    component: 'InputNumber',
    fieldName: 'orderNum',
    label: '显示排序',
    componentProps: {
      max: 9999,
    },
    defaultValue: 1,
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'leader',
    componentProps: {
      maxlength: 20,
    },
    label: '负责人',
  },
  {
    component: 'Input',
    fieldName: 'phone',
    label: '联系电话',
    componentProps: {
      maxlength: 11,
    },
    rules: z
      .string()
      .regex(mobileRegex, { message: '无效的手机号' })
      .optional(),
  },
  {
    component: 'Input',
    fieldName: 'email',
    componentProps: {
      maxlength: 50,
    },
    label: '邮箱',
    rules: z.string().regex(emailRegex, { message: '无效的邮箱' }).optional(),
  },
  {
    component: 'RadioGroup',
    fieldName: 'status',
    label: '状态',
    defaultValue: '0',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
      isButton: true,
    },
  },
];
