import type { LoginInfo } from './model';

import type { BaseResult } from '#/api/base-result';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  baseApi = '/monitor/logininfor',
  cleanLoginInfo = '/monitor/logininfor/clean',
  exportLoginInfo = '/monitor/logininfor/export',
  listLoginInfo = '/monitor/logininfor/list',
  unlockLoginInfo = '/monitor/logininfor/unlock',
}
// 获取登录日志列表
export function loginInfoList(params: any) {
  return requestClient.get<BaseResult<LoginInfo[]>>(Api.listLoginInfo, {
    params,
  });
}
// 获取登录日志详情
export function loginInfoDetail(operId: number) {
  return requestClient.get<LoginInfo>(`${Api.baseApi}/${operId}`);
}
// 删除登录日志列表
export function deleteloginInfo(operIds: number[]) {
  return requestClient.delete(`${Api.baseApi}/${operIds.join(',')}`, {
    successMessageMode: 'message',
  });
}
// 清空登录日志
export function cleanloginInfo() {
  return requestClient.delete(Api.cleanLoginInfo, {
    successMessageMode: 'message',
  });
}
// 导出
export function exportloginInfo(data: Partial<LoginInfo>) {
  return commonExport(Api.exportLoginInfo, data);
}
// 账户解锁
export function unLockUser(userName: string) {
  return requestClient.get(`${Api.unlockLoginInfo}/${userName}`, {
    successMessageMode: 'message',
  });
}
