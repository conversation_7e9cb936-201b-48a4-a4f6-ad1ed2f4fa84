import type { OnLine } from './model';

import type { BaseResult } from '#/api/base-result';

import { requestClient } from '#/api/request';

enum Api {
  baseApi = '/monitor/online',
  listOnLine = '/monitor/online/list',
}
// 获取在线用户列表
export function onLineList(params: any) {
  return requestClient.get<BaseResult<OnLine[]>>(Api.listOnLine, {
    params,
  });
}
// 强制退出
export function forceUser(tokenId: string) {
  return requestClient.delete(`${Api.baseApi}/${tokenId}`, {
    successMessageMode: 'message',
  });
}
