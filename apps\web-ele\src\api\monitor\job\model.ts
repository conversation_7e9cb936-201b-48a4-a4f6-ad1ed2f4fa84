export interface Job {
  createBy?: string;
  createTime?: string;
  updateBy?: any;
  updateTime?: any;
  remark?: string;
  jobId: number;
  jobName?: string;
  jobGroup: string;
  invokeTarget?: string;
  cronExpression?: string;
  misfirePolicy?: string;
  concurrent?: string;
  status: string;
  nextValidTime?: string;
}
export interface JobLog {
  searchValue?: any;
  createBy?: any;
  createTime?: string;
  updateBy?: any;
  updateTime?: any;
  remark?: any;
  jobLogId: number;
  jobName?: string;
  jobGroup: string;
  invokeTarget?: string;
  jobMessage?: string;
  status: string;
  exceptionInfo?: string;
  startTime?: any;
  stopTime?: any;
}
