import type { Notice } from './model';

import type { BaseResult } from '#/api/base-result';

import { requestClient } from '#/api/request';

enum Api {
  baeApi = 'system/notice',
  listNotice = '/system/notice/list',
}

// 获取通知公告列表
export function listNotice(params?: any) {
  return requestClient.get<BaseResult<Notice[]>>(Api.listNotice, { params });
}
// 获取通知公告详情
export function detailNotice(noticeId: number) {
  return requestClient.get<Notice>(`${Api.baeApi}/${noticeId}`);
}
// 新增通知公告
export function addNotice(notice: Notice) {
  return requestClient.post(Api.baeApi, notice, {
    successMessageMode: 'message',
  });
}
// 删除通知公告
export function deleteNotice(noticeIds: number[]) {
  return requestClient.delete(`${Api.baeApi}/${noticeIds.join(',')}`, {
    successMessageMode: 'message',
  });
}
// 修改通知公告
export function updateNotice(notice: Notice) {
  return requestClient.put(Api.baeApi, notice, {
    successMessageMode: 'message',
  });
}
