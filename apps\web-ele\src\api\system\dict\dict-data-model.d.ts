export interface DictData {
  createBy: string;
  createTime: string;
  cssClass: string;
  default: boolean;
  dictCode: number;
  dictLabel: string;
  dictSort: number;
  dictType: string;
  dictValue: string;
  isDefault: string;
  listClass: string;
  remark: string;
  status: string;
  updateBy?: any;
  updateTime?: any;
}
export interface DictType {
  createBy?: string;
  createTime?: string;
  updateBy?: any;
  updateTime?: any;
  remark?: string;
  dictId: number;
  dictName?: string;
  dictType?: string;
  status?: string;
}
export interface DictTypeQuery {
  dictName?: string;
  dictType?: string;
  status?: string;
  params?: Params;
  pageNum: 1;
  pageSize: 10;
}
