<script lang="ts" setup>
import { defineEmits, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import Crontab from '#/components/crontab/src/index.vue';

const emit = defineEmits<{ cronCallBack: [string] }>();

const expressionRef = ref<string>('');
const cronTabRef = ref();
const [Modal, modalApi] = useVbenModal({
  centered: true,
  fullscreenButton: false,
  onOpenChange(isOpen) {
    if (!isOpen) {
      return;
    }
    expressionRef.value = modalApi.getData<Job>().cronExpression;
  },
  onConfirm() {
    cronTabRef.value.submitFill();
  },
});
// 重置表达式
function resetCron() {
  cronTabRef.value.clearCron();
}
/** 确定后回传值 */
function crontabFill(value: string) {
  emit('cronCallBack', value);
  modalApi.close();
}
</script>
<template>
  <Modal title="生成表达式" class="w-[800px]">
    <Crontab ref="cronTabRef" :expression="expressionRef" @fill="crontabFill" />
    <template #center-footer>
      <ElButton type="warning" @click="resetCron">重置</ElButton>
    </template>
  </Modal>
</template>
