import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { h } from 'vue';

import { DictEnum } from '@vben/constants';
import { FolderIcon, MenuIcon, OkButtonIcon, VbenIcon } from '@vben/icons';
import { $t } from '@vben/locales';
import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'menuName',
    label: $t('menus.menu-name'),
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
    },
    fieldName: 'status',
    label: $t('menus.status'),
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions(DictEnum.SYS_SHOW_HIDE),
    },
    fieldName: 'visible',
    label: $t('menus.visible'),
  },
];

// 菜单类型（M目录 C菜单 F按钮）
export const menuTypeOptions = [
  { label: $t('menus.dir'), value: 'M' },
  { label: $t('menus.menu'), value: 'C' },
  { label: $t('menus.button'), value: 'F' },
];

export const yesNoOptions = [
  { label: $t('menus.yes'), value: '0' },
  { label: $t('menus.no'), value: '1' },
];

// （M目录 C菜单 F按钮）
const menuTypes = {
  C: { icon: MenuIcon, value: $t('menus.menu') },
  F: { icon: OkButtonIcon, value: $t('menus.button') },
  M: { icon: FolderIcon, value: $t('menus.dir') },
};
export const columns: VxeGridProps['columns'] = [
  {
    title: $t('menus.menu-name'),
    field: 'menuName',
    treeNode: true,
    width: 200,
    slots: {
      // 需要i18n支持 否则返回原始值
      default: ({ row }) => $t(row.menuName),
    },
  },
  {
    title: $t('menus.icon'),
    field: 'icon',
    width: 80,
    slots: {
      default: ({ row }) => {
        if (row?.icon === '#') {
          return '';
        }
        return (
          <span class={'flex justify-center'}>
            <VbenIcon icon={row.icon} />
          </span>
        );
      },
    },
  },
  {
    title: $t('menus.order-num'),
    field: 'orderNum',
    width: 80,
  },
  {
    title: $t('menus.menu-type'),
    field: 'menuType',
    width: 80,
    slots: {
      default: ({ row }) => {
        const current = menuTypes[row.menuType as 'C' | 'F' | 'M'];
        if (!current) {
          return '未知';
        }
        return (
          <span class="flex items-center justify-center gap-1">
            {h(current.icon, { class: 'size-[18px]' })}
            <span>{current.value}</span>
          </span>
        );
      },
    },
  },
  {
    title: $t('menus.perms'),
    field: 'perms',
  },
  {
    title: $t('menus.component'),
    field: 'component',
  },
  {
    title: $t('menus.status'),
    field: 'status',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
      },
    },
  },
  {
    title: $t('menus.visible'),
    field: 'visible',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.visible, DictEnum.SYS_SHOW_HIDE);
      },
    },
  },
  {
    title: $t('menus.create-time'),
    field: 'createTime',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: $t('menus.action'),
    width: 200,
  },
];
