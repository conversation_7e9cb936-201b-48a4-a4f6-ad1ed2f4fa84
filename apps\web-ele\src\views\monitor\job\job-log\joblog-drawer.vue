<script setup lang="ts">
import type { JobLog } from '#/api/monitor/job/model';
import type { DictOption } from '#/store/dict';

import { onMounted, ref } from 'vue';

import { EllipsisText, useVbenDrawer } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';

import { jobLogDetail } from '#/api/monitor/job/joblog';
import { DictTag } from '#/components/dict';
import { getDictOptions } from '#/utils/dict';

// 获取状态字典值
const dictRef = ref<DictOption[]>([]);
const jobGroupRef = ref<DictOption[]>([]);
onMounted(() => {
  dictRef.value = getDictOptions(DictEnum.SYS_COMMON_STATUS);
  jobGroupRef.value = getDictOptions(DictEnum.SYS_JOB_GROUP);
});
// 打开侧拉传递过来的实体类
const dataRef = ref<JobLog>({ jobLogId: -1, jobGroup: '', status: '' });
const [Drawer, drawerApi] = useVbenDrawer({
  showConfirmButton: false,
  showCancelButton: false,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return;
    }
    const { jobLogId } = drawerApi.getData<JobLog>();
    dataRef.value = await jobLogDetail(jobLogId);
  },
});
</script>

<template>
  <Drawer title="详情" class="w-[650px]">
    <ElDescriptions border column="1">
      <ElDescriptionsItem label="日志序号">
        {{ dataRef.jobLogId }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="任务名称">
        {{ dataRef.jobName }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="任务分组">
        <DictTag :value="dataRef.jobGroup" :dicts="jobGroupRef" />
      </ElDescriptionsItem>
      <ElDescriptionsItem label="执行时间">
        {{ dataRef.createTime }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="调用方法">
        {{ dataRef.invokeTarget }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="日志信息">
        {{ dataRef.jobMessage }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="执行状态">
        <DictTag :value="dataRef.status" :dicts="dictRef" />
      </ElDescriptionsItem>
      <ElDescriptionsItem label="异常信息" v-if="dataRef.status === '1'">
        <EllipsisText :line="3" expand>
          {{ dataRef.exceptionInfo }}
        </EllipsisText>
      </ElDescriptionsItem>
    </ElDescriptions>
  </Drawer>
</template>
<style scoped lang="scss">
:deep(.is-bordered-label) {
  min-width: 100px !important;
  font-weight: normal !important;
}
</style>
