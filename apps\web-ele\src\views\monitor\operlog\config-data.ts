import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';

import { getDictOptions } from '#/utils/dict';

export const tableColumns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    field: 'operId',
    title: '日志编号',
  },
  {
    field: 'title',
    title: '系统模块',
  },
  {
    field: 'businessType',
    title: '操作类型',
    slots: { default: 'businessType' },
  },
  {
    field: 'operName',
    title: '操作员',
  },
  {
    field: 'operIp',
    title: '操作地址',
  },
  {
    field: 'operLocation',
    title: '操作地点',
  },
  {
    field: 'status',
    title: '操作状态',
    slots: { default: 'status' },
  },
  {
    field: 'operTime',
    title: '操作日期',
    minWidth: 80,
  },
  {
    field: 'costTime',
    title: '消耗时间',
    slots: { default: 'costTime' },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
  },
];

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    label: '操作地址',
    fieldName: 'operIp',
  },
  {
    component: 'Input',
    label: '系统模块',
    fieldName: 'title',
  },
  {
    component: 'Input',
    label: '操作人员',
    fieldName: 'operName',
  },
  {
    component: 'Select',
    label: '操作类型',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_OPER_TYPE),
    },
    fieldName: 'businessType',
  },
  {
    component: 'Select',
    label: '操作状态',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_COMMON_STATUS),
    },
    fieldName: 'status',
  },
  {
    component: 'DatePicker',
    componentProps: {
      type: 'daterange',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
    },
    fieldName: 'operTime',
    label: '操作时间',
  },
];
