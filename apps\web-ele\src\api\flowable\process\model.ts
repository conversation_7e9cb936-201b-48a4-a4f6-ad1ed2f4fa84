// 部署信息
export interface Deployment {
  id?: string;
  name?: string;
  category?: string;
  key?: string;
  tenantId?: string;
  deploymentTime?: string;
}

// 导出查询参数
export interface ExportQuery {
  deploymentId?: string;
  resourceName?: string;
}

// 流程表单数据查询参数
export interface FlowFormDataQuery {
  taskId?: string;
  procInsId?: string;
}

// 流程任务信息查询参数
export interface FlowTaskInfoQuery {
  taskId?: string;
  procInsId?: string;
}

// 我的流程查询参数
export interface MyProcessQuery {
  pageNum?: number;
  pageSize?: number;
  name?: string;
  category?: string;
  key?: string;
}

// 流程启动参数
export interface ProcessStartData {
  businessKey?: string;
  variables?: Record<string, any>;
  [key: string]: any;
}

// 停止流程数据
export interface StopProcessData {
  procInsId: string;
  deleteReason?: string;
}

// 任务完成数据
export interface TaskCompleteData {
  taskId: string;
  procInsId?: string;
  comment?: string;
  variables?: Record<string, any>;
  targetUserIds?: string[];
  targetRoleIds?: string[];
  [key: string]: any;
}

// 任务驳回数据
export interface TaskRejectData {
  taskId: string;
  procInsId?: string;
  comment?: string;
  targetKey?: string;
}

// 退回任务列表数据
export interface ReturnListData {
  taskId: string;
  procInsId?: string;
}