import type { DeptResp } from './model';

import type { BaseResult } from '#/api/base-result';
import type { AuthRoleUserQuery, Role } from '#/api/system/role/model';
import type { User } from '#/api/system/user/model';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  baseApi = '/system/role',
  exportRole = '/system/role/export',
  roleAuthUsers = 'system/role/authUser/allocatedList',
  roleDeptTree = '/system/role/deptTree',
  roleList = '/system/role/list',
  rolePerms = 'system/role/dataScope',
  roleStatus = '/system/role/changeStatus',
  roleUnAuthUsers = 'system/role/authUser/unallocatedList',
  roleUsers = '/system/role/authUser/selectAll',
  unAllRoleUsers = '/system/role/authUser/cancelAll',
  unRoleUser = 'system/role/authUser/cancel',
}

// 角色列表接口
export function getRoleList(params?: any) {
  return requestClient.get<BaseResult<Role[]>>(Api.roleList, { params });
}
// 获取角色详情
export function getRoleDetail(roleId: number) {
  return requestClient.get<Role>(`${Api.baseApi}/${roleId}`);
}
// 修改角色接口
export function modifyRole(role: Role) {
  return requestClient.put(Api.baseApi, role, {
    successMessageMode: 'message',
  });
}
// 修改角色状态
export function modifyRoleStatus(role: Role) {
  return requestClient.put(Api.roleStatus, role, {
    successMessageMode: 'message',
  });
}
// 新增角色接口
export function addRole(role: Role) {
  return requestClient.post(Api.baseApi, role, {
    successMessageMode: 'message',
  });
}
// 删除角色接口
export function deleteRole(roleIds: number[]) {
  return requestClient.delete(`${Api.baseApi}/${roleIds}`, {
    successMessageMode: 'message',
  });
}
// 导出角色接口
export function exportRole(data: Partial<Role>) {
  return commonExport(Api.exportRole, data);
}
// 获取部门树形结构
export function getDeptTree(roleId: number) {
  return requestClient.get<DeptResp>(`${Api.roleDeptTree}/${roleId}`);
}
// 修改角色数据权限
export function updateRolePerms(role: Role) {
  return requestClient.put(Api.rolePerms, role, {
    successMessageMode: 'message',
  });
}
// 获取角色下已经授权的用户
export function authRoleUser(params: AuthRoleUserQuery) {
  return requestClient.get<BaseResult<User>>(Api.roleAuthUsers, { params });
}
// 获取角色下没有授权的用户
export function unAuthRoleUser(params: AuthRoleUserQuery) {
  return requestClient.get<BaseResult<User>>(Api.roleUnAuthUsers, { params });
}
// 批量授权用户赋予其角色
export function updateRoleUsers(roleId: number, userIds: number[]) {
  return requestClient.put(
    `${Api.roleUsers}?roleId=${roleId}&userIds=${userIds.join(',')}`,
    { successMessageMode: 'message' },
  );
}
// 批量取消授权
export function unAllRoleUsers(roleId: string, userIds: number[]) {
  return requestClient.put(
    `${Api.unAllRoleUsers}?roleId=${roleId}&userIds=${userIds.join(',')}`,
    { successMessageMode: 'message' },
  );
}
// 单个取消授权
export function unRoleUsers(roleId: string, userId: number) {
  return requestClient.put(
    Api.unRoleUser,
    {
      roleId,
      userId,
    },
    { successMessageMode: 'message' },
  );
}
