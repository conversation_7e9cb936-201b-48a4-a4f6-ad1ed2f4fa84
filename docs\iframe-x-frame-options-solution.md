# X-Frame-Options 问题解决方案

## 问题描述

当尝试在 iframe 中嵌入外部网站时，如果目标网站设置了 `X-Frame-Options: sameorigin` 或 `X-Frame-Options: deny`，浏览器会阻止 iframe 加载，并显示类似以下错误：

```
Refused to display 'https://example.com/' in a frame because it set 'X-Frame-Options' to 'sameorigin'.
```

## 解决方案

本项目通过代理的方式解决此问题：

1. **开发环境**：使用 Vite 代理配置
2. **生产环境**：使用后端代理配置

## 已实现的功能

### 1. 通用代理工具 (`packages/utils/src/iframe-proxy.ts`)

提供了以下工具函数：

- `generateIframeSrc()` - 生成 iframe 源地址
- `generateViteProxyConfig()` - 生成 Vite 代理配置
- `COMMON_PROXY_CONFIGS` - 预定义的常用代理配置
- `useIframeProxy()` - Vue 组件中使用的 composable

### 2. 已配置的代理

#### GeoVis 平台代理
- **开发环境路径**: `/proxy-geovis`
- **目标域名**: `https://cyyj.geovisearth.com`
- **生产环境路径**: `/proxy/geovis`

### 3. Vite 配置 (`apps/web-ele/vite.config.mts`)

已添加代理配置，自动移除以下响应头：
- `X-Frame-Options`
- `Content-Security-Policy`
- `X-Content-Type-Options`

## 使用方法

### 方法一：使用工具函数（推荐）

```vue
<script setup lang="ts">
import { useAppConfig } from '@vben/hooks';
import { COMMON_PROXY_CONFIGS, generateIframeSrc } from '@vben/utils';

const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);

const iframeSrc = generateIframeSrc(
  COMMON_PROXY_CONFIGS.geovis,
  '/byqyj/rySystem/swagger-ui/index.html',
  import.meta.env.DEV,
  apiURL,
);
</script>

<template>
  <iframe :src="iframeSrc" class="h-full w-full"></iframe>
</template>
```

### 方法二：手动配置

```vue
<script setup lang="ts">
import { useAppConfig } from '@vben/hooks';

const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);

const iframeSrc = import.meta.env.DEV
  ? '/proxy-geovis/your/path/here'
  : `${apiURL}/proxy/geovis/your/path/here`;
</script>

<template>
  <iframe :src="iframeSrc" class="h-full w-full"></iframe>
</template>
```

## 添加新的代理配置

### 1. 在 Vite 配置中添加代理

```typescript
// apps/web-ele/vite.config.mts
proxy: {
  // 现有配置...
  '/proxy-newsite': {
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/proxy-newsite/, ''),
    target: 'https://newsite.com',
    ws: false,
    configure: (proxy, _options) => {
      proxy.on('proxyRes', (proxyRes, req, res) => {
        delete proxyRes.headers['x-frame-options'];
        delete proxyRes.headers['X-Frame-Options'];
        delete proxyRes.headers['content-security-policy'];
        delete proxyRes.headers['Content-Security-Policy'];
      });
    },
  },
}
```

### 2. 在工具配置中添加预定义配置

```typescript
// packages/utils/src/iframe-proxy.ts
export const COMMON_PROXY_CONFIGS = {
  // 现有配置...
  newsite: {
    proxyPath: '/proxy-newsite',
    targetDomain: 'https://newsite.com',
    prodProxyPath: '/proxy/newsite',
  } as ProxyConfig,
};
```

## 生产环境配置

请参考 `docs/proxy-config-example.md` 中的详细配置说明。

## 测试验证

1. 启动开发服务器：`pnpm run dev`
2. 访问包含 iframe 的页面
3. 检查浏览器控制台是否还有 X-Frame-Options 错误
4. 验证 iframe 内容是否正常加载

## 注意事项

1. **法律合规**：确保代理的网站允许此类使用
2. **性能影响**：代理会增加一定的延迟
3. **安全考虑**：移除安全头可能带来风险，请谨慎评估
4. **维护成本**：需要维护代理配置的稳定性

## 故障排除

### 常见问题

1. **代理不生效**
   - 检查 Vite 配置是否正确
   - 确认开发服务器已重启

2. **生产环境无法访问**
   - 检查后端代理配置
   - 确认网络连通性

3. **内容显示异常**
   - 检查目标网站的响应内容
   - 确认代理路径配置正确

### 调试方法

1. 打开浏览器开发者工具
2. 查看 Network 标签页中的请求
3. 检查代理请求的响应头
4. 验证 iframe src 地址是否正确
