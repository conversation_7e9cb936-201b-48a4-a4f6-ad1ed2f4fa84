import type { DictData, DictType, DictTypeQuery } from './dict-data-model';

import type { BaseResult } from '#/api/base-result';
import type { ID, PageQuery } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  baseDictType = '/system/dict/type',
  dictDataExport = '/system/dict/data/export',
  dictDataList = '/system/dict/data/list',
  dictTypeExport = 'system/dict/type/export',
  dictTypeList = '/system/dict/type/list',
  dictTypeOption = '/system/dict/type/optionselect',
  dictTypRefresh = 'system/dict/type/refreshCache',
  root = '/system/dict/data',
}
// 添加字典数据
export function addDictData(dictData: DictData) {
  return requestClient.post(Api.root, dictData, {
    successMessageMode: 'message',
  });
}
// 添加字典数据
export function updateDictData(dictData: DictData) {
  return requestClient.put(Api.root, dictData, {
    successMessageMode: 'message',
  });
}
// 删除字典数据
export function deleteDictData(dictCodes: number[]) {
  return requestClient.delete(`${Api.root}/${dictCodes.join(',')}`, {
    successMessageMode: 'message',
  });
}
/**
 * 主要是DictTag组件使用
 * @param dictType 字典类型
 * @returns 字典数据
 */
export function dictDataInfo(dictType: string) {
  return requestClient.get<DictData[]>(`${Api.root}/type/${dictType}`);
}

/**
 * 字典数据
 * @param params 查询参数
 * @returns 字典数据列表
 */
export function dictDataList(params?: PageQuery) {
  return requestClient.get<BaseResult<DictData[]>>(Api.dictDataList, {
    params,
  });
}

/**
 * 导出字典数据
 * @param data 表单参数
 * @returns blob
 */
export function dictDataExport(data: Partial<DictData>) {
  return commonExport(Api.dictDataExport, data);
}

/**
 * 查询字典数据详细
 * @param dictCode 字典编码
 * @returns 字典数据
 */
export function dictDetailInfo(dictCode: ID) {
  return requestClient.get<DictData>(`${Api.root}/${dictCode}`);
}
// 查询字典类型列表
export function dictTypeList(params?: DictTypeQuery) {
  return requestClient.get<BaseResult<DictType[]>>(Api.dictTypeList, {
    params,
  });
}
// 查询全部字典类型列表
export function dictTypeOptions() {
  return requestClient.get<DictType[]>(Api.dictTypeOption);
}
// 查询单个字典类型的详情
export function dictTypeDetail(dictTypeId: string) {
  return requestClient.get<DictType>(`${Api.baseDictType}/${dictTypeId}`);
}
// 新增字典类型
export function addDictType(dictType: DictType) {
  return requestClient.post(Api.baseDictType, dictType, {
    successMessageMode: 'message',
  });
}
// 删除字典类型
export function deleteDictType(dictTypeIds: number[]) {
  return requestClient.delete(`${Api.baseDictType}/${dictTypeIds.join(',')}`, {
    successMessageMode: 'message',
  });
}
// 修改字典类型
export function updateDictType(dictType: DictType) {
  return requestClient.put(Api.baseDictType, dictType, {
    successMessageMode: 'message',
  });
}
// 字典类型导出
export function exportDictType(data: Partial<DictType>) {
  return commonExport(Api.dictTypeExport, data);
}
// 刷新字典类型
export function refreshDictType() {
  return requestClient.delete(Api.dictTypRefresh, {
    successMessageMode: 'message',
  });
}
