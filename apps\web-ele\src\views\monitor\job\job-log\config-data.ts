import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';

import { getDictOptions } from '#/utils/dict';

export const tableColumns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    field: 'jobLogId',
    title: '日志编号',
  },
  {
    field: 'jobName',
    title: '任务名称',
  },
  {
    field: 'jobGroup',
    title: '任务组名',
    slots: { default: 'jobGroup' },
  },
  {
    field: 'invokeTarget',
    title: '调用目标字符串',
  },
  {
    field: 'jobMessage',
    title: '日志信息',
  },
  {
    field: 'status',
    title: '执行状态',
    slots: { default: 'status' },
  },
  {
    field: 'createTime',
    title: '执行时间',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
  },
];

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    label: '任务名称',
    fieldName: 'jobName',
  },
  {
    component: 'Select',
    label: '任务组名',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_JOB_GROUP),
    },
    fieldName: 'jobGroup',
  },
  {
    component: 'Select',
    label: '执行',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_COMMON_STATUS),
    },
    fieldName: 'status',
  },
  {
    component: 'DatePicker',
    componentProps: {
      type: 'daterange',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
    },
    fieldName: 'createTime',
    label: '操作时间',
  },
];
