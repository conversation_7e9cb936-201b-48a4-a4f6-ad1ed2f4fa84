import type { ExpressionQuery } from './model';

import type { BaseResult } from '#/api/base-result';

import { requestClient } from '#/api/request';

// 表达式信息
export interface Expression {
  id?: string;
  name?: string;
  expression?: string;
  dataType?: string;
  status?: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
}

enum Api {
  expression = '/system/expression',
  expressionList = '/system/expression/list',
}

// 查询流程表达式列表
export function listExpression(params?: ExpressionQuery) {
  return requestClient.get<BaseResult<Expression[]>>(Api.expressionList, {
    params,
  });
}

// 查询流程表达式详细
export function getExpression(id: string) {
  return requestClient.get<BaseResult<Expression>>(`${Api.expression}/${id}`);
}

// 新增流程表达式
export function addExpression(data: Expression) {
  return requestClient.post<BaseResult<any>>(Api.expression, data, { successMessageMode: 'message' });
}

// 修改流程表达式
export function updateExpression(data: Expression) {
  return requestClient.put<BaseResult<any>>(Api.expression, data, { successMessageMode: 'message' });
}

// 删除流程表达式
export function delExpression(id: string) {
  return requestClient.delete<BaseResult<any>>(`${Api.expression}/${id}`, { successMessageMode: 'message' });
}