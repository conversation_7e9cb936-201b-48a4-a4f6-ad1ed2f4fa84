// 流程定义查询参数
export interface DefinitionQuery {
  pageNum?: number;
  pageSize?: number;
  name?: string;
  key?: string;
  category?: string;
}

// 流程定义信息
export interface ProcessDefinition {
  id?: string;
  name?: string;
  key?: string;
  category?: string;
  version?: number;
  deploymentId?: string;
  resourceName?: string;
  description?: string;
  suspended?: boolean;
}

// 用户列表查询参数
export interface UserListQuery {
  pageNum?: number;
  pageSize?: number;
  userName?: string;
  nickName?: string;
}

// 角色列表查询参数
export interface RoleListQuery {
  pageNum?: number;
  pageSize?: number;
  roleName?: string;
  roleKey?: string;
}

// 表达式列表查询参数
export interface ExpListQuery {
  pageNum?: number;
  pageSize?: number;
  expression?: string;
}

// 流程查看器参数
export interface FlowViewerQuery {
  procInsId?: string;
  executionId?: string;
}

// 流程XML和节点查询参数
export interface FlowXmlNodeQuery {
  procInsId?: string;
  procDefId?: string;
  taskId?: string;
}

// 保存XML数据
export interface SaveXmlData {
  name: string;
  xml: string;
  category?: string;
  description?: string;
}

// 部署信息
export interface Deployment {
  id?: string;
  name?: string;
  category?: string;
  key?: string;
  tenantId?: string;
  deploymentTime?: string;
}

// 流程启动参数
export interface ProcessStartData {
  businessKey?: string;
  variables?: Record<string, any>;
  [key: string]: any;
}

// 流程变量
export interface ProcessVariables {
  [key: string]: any;
}

// 流程状态更新参数
export interface ProcessStateParams {
  id: string;
  suspended: boolean;
}

// 导出查询参数
export interface ExportQuery {
  deploymentId?: string;
  resourceName?: string;
}