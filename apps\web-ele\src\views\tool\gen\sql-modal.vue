<script setup lang="ts">
import { defineEmits, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { createTable } from '#/api/tool/gen/gen';

const emit = defineEmits<{ reload: [] }>();

const sqlRef = ref<string>('');
const [Modal, modelApi] = useVbenModal({
  centered: true,
  async onConfirm() {
    if (sqlRef.value === '') {
      return;
    }
    await createTable(sqlRef.value);
    emit('reload');
    await modelApi.close();
  },
  onClosed() {
    sqlRef.value = '';
  },
});
</script>

<template>
  <Modal title="创建表" class="h-[500px] w-[800px]">
    <div class="mb-1.5">创建表语句(支持多个建表语句)：</div>
    <ElInput
      type="textarea"
      placeholder="请输入文本"
      v-model="sqlRef"
      :rows="15"
    />
  </Modal>
</template>
