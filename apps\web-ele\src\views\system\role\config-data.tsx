import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { ID } from '#/api/common';
import type { MenuOption } from '#/api/system/menu/model';

import { DictEnum } from '@vben/constants';

import { getDictOptions } from '#/utils/dict';

export interface Permission {
  checked: boolean;
  id: ID;
  label: string;
}

export interface MenuPermissionOption extends MenuOption {
  permissions: Permission[];
}

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'roleId',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    component: 'Input',
    fieldName: 'roleName',
    componentProps: {
      maxlength: 30,
    },
    label: '角色名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'roleKey',
    componentProps: {
      maxlength: 100,
    },
    label: '权限字符',
    rules: 'required',
  },
  {
    component: 'InputNumber',
    fieldName: 'roleSort',
    componentProps: {
      max: 9999,
    },
    label: '角色排序',
    defaultValue: 0,
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
    },
    fieldName: 'status',
    defaultValue: '0',
    label: '角色状态',
    rules: 'required',
  },
  {
    component: 'CheckboxGroup',
    fieldName: 'checkboxGroup',
    label: '菜单权限',
  },
  {
    component: 'ElTree',
    fieldName: 'permTree',
    label: '',
  },
  {
    component: 'Input',
    componentProps: {
      type: 'textarea',
      maxlength: 500,
      showWordLimit: true,
    },
    fieldName: 'remark',
    formItemClass: 'items-baseline',
    label: '备注',
  },
];

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'roleName',
    label: '角色名称',
  },
  {
    component: 'Input',
    fieldName: 'roleKey',
    label: '权限字符',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
    },
    fieldName: 'status',
    label: '角色状态',
  },
  {
    component: 'DatePicker',
    componentProps: {
      type: 'daterange',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
    },
    fieldName: 'createTime',
    label: '创建时间',
  },
];

export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    type: 'seq',
    title: '序号',
    width: 70,
  },
  {
    title: '角色名称',
    field: 'roleName',
    minWidth: 130,
  },
  {
    field: 'roleKey',
    title: '权限字符',
    slots: { default: 'roleKey' },
    minWidth: 80,
  },
  {
    title: '显示顺序',
    field: 'roleSort',
    minWidth: 80,
  },
  {
    title: '角色状态',
    field: 'status',
    slots: { default: 'status' },
    minWidth: 80,
  },
  {
    title: '创建时间',
    field: 'createTime',
    minWidth: 150,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    resizable: false,
    title: '操作',
    width: 180,
  },
];

const permScope = [
  {
    value: '1',
    label: '全部数据权限',
  },
  {
    value: '2',
    label: '自定数据权限',
  },
  {
    value: '3',
    label: '本部门数据权限',
  },
  {
    value: '4',
    label: '本部门及以下数据权限',
  },
  {
    value: '5',
    label: '仅本人数据权限',
  },
];

export const permissionDrawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'roleId',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    component: 'Input',
    fieldName: 'roleName',
    disabled: true,
    label: '角色名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'roleKey',
    disabled: true,
    label: '权限字符',
    rules: 'required',
  },
  {
    component: 'Select',
    fieldName: 'dataScope',
    componentProps: {
      options: permScope,
      fieldName: 'label',
      valueField: 'value',
    },
    label: '权限范围',
    defaultValue: '1',
  },
  {
    component: 'CheckboxGroup',
    fieldName: 'checkboxGroup',
    label: '数据权限',
    dependencies: {
      show: (values) => {
        return values.dataScope === '2';
      },
      triggerFields: ['dataScope'],
    },
  },
  {
    component: 'ElTree',
    fieldName: 'permTree',
    dependencies: {
      show: (values) => {
        return values.dataScope === '2';
      },
      triggerFields: ['dataScope'],
    },
    label: '',
  },
];
