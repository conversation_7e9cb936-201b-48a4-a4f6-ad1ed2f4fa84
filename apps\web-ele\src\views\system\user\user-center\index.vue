<script setup lang="ts">
import type { User } from '#/api/system/user/model';

import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import { userProfile } from '#/api/system/user/user';

import BasicInfo from './basic-info.vue';
import BasicSet from './basic-set.vue';

const userInfoRef = ref<User>({
  userId: -100,
  status: '',
});
const postRef = ref<string>('');
// 获取用户信息
async function profile() {
  const data = await userProfile();
  if (data.code === 200) {
    userInfoRef.value = data.data;
    postRef.value = data.postGroup ?? '';
  } else {
    ElMessage.error(data.msg);
  }
}
onMounted(async () => {
  await profile();
});
</script>

<template>
  <Page>
    <div class="flex flex-col gap-[16px] lg:flex-row">
      <BasicInfo :user="userInfoRef" :post="postRef" />
      <BasicSet :user="userInfoRef" @reload="profile" />
    </div>
  </Page>
</template>
