import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';

import { getDictOptions } from '#/utils/dict';

export const tableColumns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    field: 'postId',
    title: '岗位编号',
  },
  {
    field: 'postCode',
    title: '岗位编码',
  },
  {
    field: 'postName',
    title: '岗位名称',
  },
  {
    field: 'postSort',
    title: '岗位排序',
  },
  {
    field: 'status',
    title: '状态',
    slots: { default: 'status' },
  },
  {
    field: 'createTime',
    title: '创建时间',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
  },
];

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    label: '岗位编码',
    fieldName: 'postCode',
  },
  {
    component: 'Input',
    label: '岗位名称',
    fieldName: 'postName',
  },
  {
    component: 'Select',
    label: '状态',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
    },
    fieldName: 'status',
  },
];
export const drawerFormSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'postId',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    component: 'Input',
    label: '岗位名称',
    componentProps: {
      maxlength: 50,
    },
    fieldName: 'postName',
    rules: 'required',
  },
  {
    component: 'Input',
    label: '岗位编码',
    componentProps: {
      maxlength: 64,
    },
    fieldName: 'postCode',
    rules: 'required',
  },
  {
    component: 'InputNumber',
    label: '岗位排序',
    componentProps: {
      max: 9999,
    },
    fieldName: 'postSort',
    defaultValue: '0',
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    label: '状态',
    componentProps: {
      isButton: true,
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
    },
    fieldName: 'status',
    defaultValue: '0',
  },
  {
    component: 'Input',
    label: '备注',
    componentProps: {
      type: 'textarea',
      maxlength: 500,
      showWordLimit: true,
    },
    fieldName: 'remark',
  },
];
