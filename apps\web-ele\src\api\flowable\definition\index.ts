import type {
  DefinitionQuery,
  Deployment,
  ExportQuery,
  ExpListQuery,
  FlowViewerQuery,
  FlowXmlNodeQuery,
  ProcessDefinition,
  ProcessStartData,
  ProcessStateParams,
  ProcessVariables,
  RoleListQuery,
  SaveXmlData,
  UserListQuery,
} from './model';

import type { BaseResult } from '#/api/base-result';

import { requestClient } from '#/api/request';

enum Api {
  definitionList = '/flowable/definition/list',
  definitionStart = '/flowable/definition/start',
  processVariables = '/flowable/task/processVariables',
  updateState = '/flowable/definition/updateState',
  userList = '/flowable/definition/userList',
  roleList = '/flowable/definition/roleList',
  expList = '/flowable/definition/expList',
  readXml = '/flowable/definition/readXml',
  readImage = '/flowable/definition/readImage',
  flowViewer = '/flowable/task/flowViewer',
  flowXmlAndNode = '/flowable/task/flowXmlAndNode',
  saveXml = '/flowable/definition/save',
  deployment = '/system/deployment',
  deleteDefinition = '/flowable/definition',
  exportDeployment = '/system/deployment/export',
}

// 查询流程定义列表
export function listDefinition(params?: DefinitionQuery) {
  return requestClient.get<BaseResult<ProcessDefinition[]>>(Api.definitionList, {
    params,
  });
}

// 部署流程实例
export function definitionStart(procDefId: string, data: ProcessStartData) {
  return requestClient.post<BaseResult<any>>(
    `${Api.definitionStart}/${procDefId}`,
    data,
    { successMessageMode: 'message' },
  );
}

// 获取流程变量
export function getProcessVariables(taskId: string) {
  return requestClient.get<BaseResult<ProcessVariables>>(
    `${Api.processVariables}/${taskId}`,
  );
}

// 激活/挂起流程
export function updateState(params: ProcessStateParams) {
  return requestClient.put<BaseResult<any>>(Api.updateState, null, { params, successMessageMode: 'message' });
}

// 指定流程办理人员列表
export function userList(params?: UserListQuery) {
  return requestClient.get<BaseResult<any[]>>(Api.userList, { params });
}

// 指定流程办理组列表
export function roleList(params?: RoleListQuery) {
  return requestClient.get<BaseResult<any[]>>(Api.roleList, { params });
}

// 指定流程表达式
export function expList(params?: ExpListQuery) {
  return requestClient.get<BaseResult<any[]>>(Api.expList, { params });
}

// 读取xml文件
export function readXml(deployId: string) {
  return requestClient.get<BaseResult<string>>(`${Api.readXml}/${deployId}`);
}

// 读取image文件
export function readImage(deployId: string) {
  return requestClient.get<BaseResult<string>>(`${Api.readImage}/${deployId}`);
}

// 获取流程执行节点
export function getFlowViewer(procInsId: string, executionId: string) {
  return requestClient.get<BaseResult<any>>(
    `${Api.flowViewer}/${procInsId}/${executionId}`,
  );
}

// 流程节点数据
export function flowXmlAndNode(params?: FlowXmlNodeQuery) {
  return requestClient.get<BaseResult<any>>(Api.flowXmlAndNode, { params });
}

// 保存xml文件
export function saveXml(data: SaveXmlData) {
  return requestClient.post<BaseResult<any>>(Api.saveXml, data, { successMessageMode: 'message' });
}

// 新增流程定义
export function addDeployment(data: Deployment) {
  return requestClient.post<BaseResult<any>>(Api.deployment, data, { successMessageMode: 'message' });
}

// 修改流程定义
export function updateDeployment(data: Deployment) {
  return requestClient.put<BaseResult<any>>(Api.deployment, data, { successMessageMode: 'message' });
}

// 删除流程定义
export function delDeployment(deployId: string) {
  return requestClient.delete<BaseResult<any>>(
    `${Api.deleteDefinition}/${deployId}`,
    { successMessageMode: 'message' },
  );
}

// 导出流程定义
export function exportDeployment(params?: ExportQuery) {
  return requestClient.get<BaseResult<any>>(Api.exportDeployment, { params });
}