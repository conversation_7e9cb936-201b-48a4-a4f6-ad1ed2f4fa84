import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'tableName',
    label: '表名称',
  },
  {
    component: 'Input',
    fieldName: 'tableComment',
    label: '表描述',
  },
  {
    component: 'DatePicker',
    componentProps: {
      type: 'daterange',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
    },
    fieldName: 'createTime',
    label: '创建时间',
  },
];

export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    type: 'seq',
    title: '序号',
    width: 70,
  },
  {
    title: '表名称',
    field: 'tableName',
  },
  {
    field: 'tableComment',
    title: '表描述',
  },
  {
    title: '实体',
    field: 'className',
  },
  {
    title: '创建时间',
    field: 'createTime',
  },
  {
    title: '更新时间',
    field: 'updateTime',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    resizable: false,
    title: '操作',
    width: 180,
  },
];

export const modalQuerySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'tableName',
    label: '表名称',
  },
  {
    component: 'Input',
    fieldName: 'tableComment',
    label: '表描述',
  },
];

export const modalColumns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    title: '表名称',
    field: 'tableName',
  },
  {
    field: 'tableComment',
    title: '表描述',
  },
  {
    title: '创建时间',
    field: 'createTime',
  },
  {
    title: '更新时间',
    field: 'updateTime',
  },
];
