import type { Job } from './model';

import type { BaseResult } from '#/api/base-result';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  baseApi = '/monitor/job',
  changeStatusJob = '/monitor/job/changeStatus',
  exportJob = '/monitor/job/export',
  jobList = '/monitor/job/list',
  runJob = '/monitor/job/run',
}

// 获取列表
export function listJob(params: any) {
  return requestClient.get<BaseResult<Job[]>>(Api.jobList, {
    params,
  });
}
// 获取详情
export function jobDetail(jobId: number) {
  return requestClient.get<Job>(`${Api.baseApi}/${jobId}`);
}
// 新增job
export function addJob(job: Job) {
  return requestClient.post(Api.baseApi, job, {
    successMessageMode: 'message',
  });
}
// 运行一次job
export function jobRun(jobGroup: string, jobId: number) {
  return requestClient.put(
    Api.runJob,
    { jobGroup, jobId },
    {
      successMessageMode: 'message',
    },
  );
}
// 修改
export function updateJob(job: Job) {
  return requestClient.put(Api.baseApi, job, {
    successMessageMode: 'message',
  });
}
// 删除列表
export function deleteJob(jobIds: number[]) {
  return requestClient.delete(`${Api.baseApi}/${jobIds.join(',')}`, {
    successMessageMode: 'message',
  });
}
// 导出
export function exportJob(data: Partial<Job>) {
  return commonExport(Api.exportJob, data);
}
// 开关定时任务
export function changeStatus(jobId: number, status: string) {
  return requestClient.put(
    Api.changeStatusJob,
    { jobId, status },
    {
      successMessageMode: 'message',
    },
  );
}
