<script setup lang="ts">
import type { Server } from '#/api/monitor/server/model';

import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { Coffee, Computer, Cpu, Disk, Memory } from '@vben/icons';

import { getServer } from '#/api/monitor/server/server';

const serverRef = ref<Server>({});
onMounted(async () => {
  serverRef.value = await getServer();
});
</script>

<template>
  <Page auto-content-height>
    <div class="grid w-full grid-cols-2 gap-4">
      <ElCard>
        <div class="mb-2 flex items-center gap-0.5 text-[16px]">
          <Cpu />
          <span>CPU</span>
        </div>
        <table>
          <thead>
            <tr>
              <th>属性</th>
              <th>值</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>核心数</td>
              <td>{{ serverRef.cpu?.cpuNum }}</td>
            </tr>
            <tr>
              <td>用户使用率</td>
              <td>{{ serverRef.cpu?.used }}%</td>
            </tr>
            <tr>
              <td>系统使用率</td>
              <td>{{ serverRef.cpu?.sys }}%</td>
            </tr>
            <tr>
              <td>当前空闲率</td>
              <td>{{ serverRef.cpu?.free }}%</td>
            </tr>
          </tbody>
        </table>
      </ElCard>
      <ElCard>
        <div class="mb-2 flex items-center gap-0.5 text-[16px]">
          <Memory />
          <span>内存</span>
        </div>
        <table>
          <thead>
            <tr>
              <th>属性</th>
              <th>内存</th>
              <th>JVM</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>总内存</td>
              <td>{{ serverRef.mem?.total }}G</td>
              <td>{{ serverRef.jvm?.total }}M</td>
            </tr>
            <tr>
              <td>已用内存</td>
              <td>{{ serverRef.mem?.used }}G</td>
              <td>{{ serverRef.jvm?.used }}M</td>
            </tr>
            <tr>
              <td>剩余内存</td>
              <td>{{ serverRef.mem?.free }}G</td>
              <td>{{ serverRef.jvm?.free }}M</td>
            </tr>
            <tr>
              <td>使用率</td>
              <td :class="{ 'text-danger': serverRef.mem?.usage ?? 0 > 80 }">
                {{ serverRef.mem?.usage }}%
              </td>
              <td :class="{ 'text-danger': serverRef.mem?.free ?? 0 > 80 }">
                {{ serverRef.jvm?.free }}%
              </td>
            </tr>
          </tbody>
        </table>
      </ElCard>
      <ElCard class="col-span-2">
        <div class="mb-2 flex items-center gap-0.5 text-[16px]">
          <Computer />
          <span>服务器信息</span>
        </div>
        <table>
          <tbody class="tr-left">
            <tr>
              <td>服务器名称</td>
              <td>{{ serverRef.sys?.computerName }}</td>
              <td>操作系统</td>
              <td>{{ serverRef.sys?.osName }}</td>
            </tr>
            <tr>
              <td>服务器IP</td>
              <td>{{ serverRef.sys?.computerIp }}</td>
              <td>系统架构</td>
              <td>{{ serverRef.sys?.osArch }}</td>
            </tr>
          </tbody>
        </table>
      </ElCard>
      <ElCard class="col-span-2">
        <div class="mb-2 flex items-center gap-0.5 text-[16px]">
          <Coffee />
          <span>Java虚拟机信息</span>
        </div>
        <table>
          <tbody class="tr-left">
            <tr>
              <td>Java名称</td>
              <td>{{ serverRef.jvm?.name }}</td>
              <td>Java版本</td>
              <td>{{ serverRef.jvm?.version }}</td>
            </tr>
            <tr>
              <td>启动时间</td>
              <td>{{ serverRef.jvm?.startTime }}</td>
              <td>运行时长</td>
              <td>{{ serverRef.jvm?.runTime }}</td>
            </tr>
            <tr>
              <td>安装路径</td>
              <td class="th-col-flex">{{ serverRef.jvm?.home }}</td>
            </tr>
            <tr>
              <td>项目路径</td>
              <td class="th-col-flex">{{ serverRef.sys?.userDir }}</td>
            </tr>
            <tr>
              <td>运行参数</td>
              <td class="th-col-flex">{{ serverRef.jvm?.inputArgs }}</td>
            </tr>
          </tbody>
        </table>
      </ElCard>
      <ElCard class="col-span-2">
        <div class="mb-2 flex items-center gap-0.5 text-[16px]">
          <Disk />
          <span>磁盘状态</span>
        </div>
        <table>
          <thead>
            <tr>
              <th>盘符路径</th>
              <th>文件系统</th>
              <th>盘符类型</th>
              <th>总大小</th>
              <th>可用大小</th>
              <th>已用大小</th>
              <th>已用百分比</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(sysFile, index) in serverRef.sysFiles" :key="index">
              <td>{{ sysFile?.dirName }}</td>
              <td>{{ sysFile?.sysTypeName }}</td>
              <td>{{ sysFile?.typeName }}</td>
              <td>{{ sysFile?.total }}</td>
              <td>{{ sysFile?.free }}</td>
              <td>{{ sysFile?.used }}</td>
              <td :class="{ 'text-danger': sysFile?.usage ?? 0 > 80 }">
                {{ sysFile?.usage }}%
              </td>
            </tr>
          </tbody>
        </table>
      </ElCard>
    </div>
  </Page>
</template>
<style scoped lang="scss">
table {
  width: 100%;
  font-size: 14px;

  tr {
    display: flex;
    align-items: center;
    min-height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #dfe6ec;

    td {
      flex: 1;
      text-align: center;
    }

    th {
      flex: 1;
    }
  }
}

.th-col-flex {
  flex: 3;
}

.text-danger {
  color: red;
}

.tr-left {
  tr {
    td {
      text-align: left;
    }
  }
}
</style>
