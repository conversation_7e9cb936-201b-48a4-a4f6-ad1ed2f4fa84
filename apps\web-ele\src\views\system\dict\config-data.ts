import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';

import { getDictOptions } from '#/utils/dict';

export const queryFormSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'dictName',
    label: '字典名称',
  },
  {
    component: 'Input',
    fieldName: 'dictType',
    label: '字典类型',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
    },
    fieldName: 'status',
    label: '状态',
  },
  {
    component: 'DatePicker',
    componentProps: {
      type: 'daterange',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
    },
    fieldName: 'createTime',
    label: '创建时间',
  },
];

export const tableColumns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 80,
  },
  {
    field: 'dictId',
    title: '字典编号',
  },
  {
    field: 'dictName',
    title: '字典名称',
  },
  {
    field: 'dictType',
    slots: { default: 'dictType' },
    title: '字典类型',
  },
  {
    field: 'status',
    slots: { default: 'status' },
    title: '状态',
  },
  {
    field: 'createTime',
    title: '创建时间',
  },
  {
    field: 'remark',
    title: '备注',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
  },
];

export const drawerFormSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'dictId',
  },
  {
    component: 'Input',
    fieldName: 'dictName',
    label: '字典名称',
    componentProps: {
      maxlength: 100,
    },
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'dictType',
    label: '字典类型',
    componentProps: {
      maxlength: 100,
    },
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
      isButton: true,
    },
    fieldName: 'status',
    defaultValue: '0',
    label: '状态',
  },
  {
    component: 'Input',
    fieldName: 'remark',
    label: '备注',
    componentProps: {
      maxlength: 500,
      type: 'textarea',
      showWordLimit: true,
    },
  },
];
