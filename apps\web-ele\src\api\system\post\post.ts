import type { BaseResult } from '#/api/base-result';
import type { Post } from '#/api/system/post/model';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  baseApi = '/system/post',
  postExport = '/system/post/export',
  postList = '/system/post/list',
}

// 新增岗位
export function addPost(post: Post) {
  return requestClient.post(Api.baseApi, post, {
    successMessageMode: 'message',
  });
}
// 修改岗位
export function updatePost(post: Post) {
  return requestClient.put(Api.baseApi, post, {
    successMessageMode: 'message',
  });
}
// 删除岗位
export function deletePost(postId: number[]) {
  return requestClient.delete(`${Api.baseApi}/${postId.join(',')}`, {
    successMessageMode: 'message',
  });
}
// 获取岗位详情
export function detailPost(postId: number) {
  return requestClient.get(`${Api.baseApi}/${postId}`);
}
// 岗位列表接口
export function getPostList(params?: any) {
  return requestClient.get<BaseResult<Post[]>>(Api.postList, { params });
}
// 导出
export function exportPost(data: Partial<Post>) {
  return commonExport(Api.postExport, data);
}
