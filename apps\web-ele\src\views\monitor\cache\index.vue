<script setup lang="ts">
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { Cache } from '#/api/monitor/cache/model';

import { onActivated, onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { Charts, Computer, Memory } from '@vben/icons';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { getCache } from '#/api/monitor/cache/cache';

// 左侧饼状图
const chartRef = ref<EchartsUIType>();
const commandCharts = useEcharts(chartRef);
// 右侧仪表盘图
const memoryRef = ref<EchartsUIType>();
const memoryCharts = useEcharts(memoryRef);

const cacheRef = ref<Cache>({});
onMounted(async () => {
  // 获取接口数据
  cacheRef.value = await getCache();
  // 命令饼状图配置
  await commandCharts.renderEcharts({
    legend: {
      bottom: '2%',
      left: 'center',
    },
    series: [
      {
        color: ['#5ab1ef', '#b6a2de', '#67e0e3', '#2ec7c9'],
        data: [],
        emphasis: {
          label: {
            fontSize: '12',
            fontWeight: 'bold',
            show: true,
          },
        },
        itemStyle: {
          // borderColor: '#fff',
          borderRadius: 4,
          borderWidth: 2,
        },
        label: {
          position: 'center',
          show: false,
        },
        labelLine: {
          show: false,
        },
        name: '命令',
        radius: '70%',
        type: 'pie',
      },
    ],
    tooltip: {
      trigger: 'item',
    },
  });
  commandCharts.getChartInstance()?.setOption({
    series: [{ data: cacheRef.value.commandStats }],
  });
  // 内存峰值图配置
  await memoryCharts.renderEcharts({
    series: [
      {
        name: '峰值',
        type: 'gauge',
        radius: '80%',
        min: 0,
        max: 1000,
        axisLine: {
          lineStyle: {
            width: 20,
            color: [
              [0.3, '#67e0e3'],
              [0.7, '#37a2da'],
              [1, '#fd666d'],
            ],
          },
        },
        axisTick: {
          distance: -30,
          length: 8,
          lineStyle: {
            color: '#fff',
            width: 2,
          },
        },
        splitLine: {
          distance: -30,
          length: 30,
          lineStyle: {
            color: '#fff',
            width: 4,
          },
        },
        axisLabel: {
          color: 'inherit',
          distance: 40,
          fontSize: 14,
        },
        detail: {
          formatter: cacheRef.value.info?.used_memory_human,
          fontSize: 14,
        },
        data: [
          {
            value: Number.parseFloat(
              cacheRef.value.info?.used_memory_human ?? '0',
            ),
            name: '内存消耗',
          },
        ],
      },
    ],
    tooltip: {
      trigger: 'item',
    },
  });
});
// 不重置，切换菜单图标会从做下角缩放上来
onActivated(() => {
  commandCharts.resize(false);
  memoryCharts.resize(false);
});
</script>

<template>
  <Page auto-content-height>
    <div class="grid w-full grid-cols-2 gap-4">
      <ElCard class="col-span-2">
        <div class="mb-2 flex items-center gap-0.5 text-[16px]">
          <Computer />
          <span>基本信息</span>
        </div>
        <table>
          <tbody class="tr-left">
            <tr>
              <td>Redis版本</td>
              <td>{{ cacheRef.info?.redis_version }}</td>
              <td>运行模式</td>
              <td>
                {{
                  cacheRef.info?.redis_mode === 'standalone' ? '单机' : '集群'
                }}
              </td>
              <td>端口</td>
              <td>{{ cacheRef.info?.tcp_port }}</td>
              <td>客户端数</td>
              <td>{{ cacheRef.info?.connected_clients }}</td>
            </tr>
            <tr>
              <td>运行时间(天)</td>
              <td>{{ cacheRef.info?.uptime_in_days }}</td>
              <td>使用内存</td>
              <td>{{ cacheRef.info?.used_memory_human }}</td>
              <td>使用CPU</td>
              <td>
                {{
                  parseFloat(
                    cacheRef.info?.used_cpu_user_children ?? '0',
                  ).toFixed(2)
                }}
              </td>
              <td>内存配置</td>
              <td>{{ cacheRef.info?.maxmemory_human }}</td>
            </tr>
            <tr>
              <td>AOF是否开启</td>
              <td>{{ cacheRef.info?.aof_enabled === '0' ? '否' : '是' }}</td>
              <td>RDB是否成功</td>
              <td>{{ cacheRef.info?.rdb_last_bgsave_status }}</td>
              <td>Key数量</td>
              <td>{{ cacheRef?.dbSize }}</td>
              <td>网络入口/出口</td>
              <td>
                {{ cacheRef.info?.instantaneous_input_kbps }}kps/{{
                  cacheRef.info?.instantaneous_output_kbps
                }}kps
              </td>
            </tr>
          </tbody>
        </table>
      </ElCard>
      <ElCard>
        <div class="mb-2 flex items-center gap-0.5 text-[16px]">
          <Charts />
          <span>命令统计</span>
        </div>
        <EchartsUI ref="chartRef" height="400px" />
      </ElCard>
      <ElCard>
        <div class="mb-2 flex items-center gap-0.5 text-[16px]">
          <Memory />
          <span>内存信息</span>
        </div>
        <EchartsUI ref="memoryRef" height="400px" />
      </ElCard>
    </div>
  </Page>
</template>
<style scoped lang="scss">
table {
  width: 100%;
  font-size: 14px;

  tr {
    display: flex;
    align-items: center;
    min-height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #dfe6ec;

    td {
      flex: 1;
      text-align: center;
    }

    th {
      flex: 1;
    }
  }
}

.th-col-flex {
  flex: 3;
}

.text-danger {
  color: red;
}

.tr-left {
  tr {
    td {
      text-align: left;
    }
  }
}
</style>
