import type { Cache, CacheDetail } from './model';

import { requestClient } from '#/api/request';

enum Api {
  baseApi = '/monitor/cache',
  cacheDetail = '/monitor/cache/getValue/sys_config',
  cacheDetailList = '/monitor/cache/getNames',
  cacheKey = '/monitor/cache/clearCacheKey',
  cacheKeys = '/monitor/cache/getKeys',
  cacheName = '/monitor/cache/clearCacheName',
  clearAll = '/monitor/cache/clearCacheAll',
}

export function getCache() {
  return requestClient.get<Cache>(Api.baseApi);
}
// 获取缓存列表
export function getCacheList() {
  return requestClient.get<CacheDetail[]>(Api.cacheDetailList);
}
// 获取key值
export function getCacheKey(key: string) {
  return requestClient.get<string[]>(`${Api.cacheKeys}/${key}`);
}
// 获取缓存详情
export function getCacheDetail(key: string) {
  return requestClient.get<CacheDetail>(`${Api.cacheDetail}/${key}`);
}
// 删除缓存name
export function deleteCacheName(name: string) {
  return requestClient.delete(`${Api.cacheName}/${name}`, {
    successMessageMode: 'message',
  });
}
// 删除缓存key
export function deleteCacheKey(key: string) {
  return requestClient.delete(`${Api.cacheKey}/${key}`, {
    successMessageMode: 'message',
  });
}
// 清空所有
export function clearCacheAll() {
  return requestClient.delete(Api.clearAll);
}
