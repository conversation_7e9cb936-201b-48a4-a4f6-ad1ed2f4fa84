import type { Department } from './model';

import { requestClient } from '#/api/request';

enum Api {
  base = '/system/dept',
  deptList = '/system/dept/list',
  deptTree = '/system/user/deptTree',
}

export interface Dept {
  children: Dept[];
  disabled: boolean;
  id: number;
  label: string;
}

/**
 * 获取部门树形图
 */
async function deptTree() {
  return requestClient.get<Dept[]>(Api.deptTree);
}

async function deptOne(id: number) {
  return requestClient.get<Dept>(`${Api.base}/${id}`);
}
// 获取部门列表
function deptList(params?: any) {
  return requestClient.get<Department>(Api.deptList, { params });
}
// 获取部门详情
function deptDetail(deptId: number) {
  return requestClient.get<Department>(`${Api.base}/${deptId}`);
}
// 新增部门
function addDept(dept: Department) {
  return requestClient.post(Api.base, dept, { successMessageMode: 'message' });
}
// 修改部门
function updateDept(dept: Department) {
  return requestClient.put(Api.base, dept, { successMessageMode: 'message' });
}
// 删除部门
function deleteDept(deptId: number) {
  return requestClient.delete(`${Api.base}/${deptId}`, {
    successMessageMode: 'message',
  });
}

export {
  addDept,
  deleteDept,
  deptDetail,
  deptList,
  deptOne,
  deptTree,
  updateDept,
};
