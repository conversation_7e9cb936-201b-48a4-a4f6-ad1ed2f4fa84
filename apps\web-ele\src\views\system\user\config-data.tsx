import type { VxeGridProps } from '#/adapter/vxe-table';

export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    title: '用户编号',
    field: 'userId',
    width: '80',
  },
  {
    title: '用户账号',
    field: 'userName',
    minWidth: 80,
  },
  {
    title: '用户昵称',
    field: 'nickName',
    minWidth: 130,
  },
  {
    field: 'avatar',
    title: '头像',
    slots: { default: 'avatar' },
    minWidth: 80,
  },
  {
    title: '手机号码',
    field: 'phonenumber',
    minWidth: 120,
  },
  {
    title: '用户状态',
    field: 'status',
    slots: { default: 'status' },
    minWidth: 80,
  },
  {
    title: '创建时间',
    field: 'createTime',
    minWidth: 150,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    resizable: false,
    title: '操作',
    width: 180,
  },
];
