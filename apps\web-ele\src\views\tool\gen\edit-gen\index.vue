<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DictType } from '#/api/system/dict/dict-data-model';
import type { Gen, TableInfo } from '#/api/tool/gen/model';

import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { dictTypeOptions } from '#/api/system/dict/dict-data';
import { detailTable, updateGen } from '#/api/tool/gen/gen';

import {
  basicSchema,
  columns,
  genSchema,
  htmlTypes,
  javaTypes,
  queryTypes,
} from './config-data';

const route = useRoute();
const tableId = route.params.tableId as string;

// 基本信息表单
const [BasicForm, basicFormApi] = useVbenForm({
  schema: basicSchema(),
  wrapperClass: 'grid-cols-2',
  handleSubmit: genSubmit,
  resetButtonOptions: {
    show: false,
  },
});
const gridOptions: VxeGridProps = {
  columns,
  pagerConfig: {
    enabled: false,
  },
};
// 字段信息
const [BasicTable, basicTableApi] = useVbenVxeGrid({
  gridOptions,
});
// 生成信息表单
const [GenForm, genFormApi] = useVbenForm({
  schema: genSchema(),
  wrapperClass: 'grid-cols-2',
  handleSubmit: genSubmit,
  resetButtonOptions: {
    show: false,
  },
});
async function genSubmit() {
  const basicValid = await basicFormApi.validate();
  const genValid = await genFormApi.validate();

  if (basicValid.valid && genValid.valid) {
    const info = await basicFormApi.getValues<TableInfo>();
    const genInfo = await genFormApi.getValues<TableInfo>();
    const tableColumns = basicTableApi.grid.getData();
    const gen: Gen = Object.assign<Gen, any, any>({}, info, genInfo);
    gen.columns = tableColumns;
    await updateGen(gen);
  } else {
    ElMessage.error('表单校验未通过，请重新检查提交内容');
  }
}
const dictsRef = ref<DictType[]>([]);
onMounted(async () => {
  dictTypeOptions().then((data) => (dictsRef.value = data));
  const data = await detailTable(tableId);
  await basicFormApi.setValues(data.info);
  await basicTableApi.grid.loadData(data.rows);
  await genFormApi.setValues(data.info);
});
</script>

<template>
  <Page auto-content-height>
    <ElTabs model-value="2" class="card-box p-4 py-6">
      <ElTabPane label="基本信息" name="1">
        <BasicForm />
      </ElTabPane>
      <ElTabPane label="字段信息" name="2">
        <BasicTable>
          <template #columnComment="{ row }">
            <ElInput v-model="row.columnComment" />
          </template>
          <template #javaType="{ row }">
            <ElSelect v-model="row.javaType">
              <ElOption
                v-for="item in javaTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElSelect>
          </template>
          <template #javaField="{ row }">
            <ElInput v-model="row.javaField" />
          </template>
          <template #isInsert="{ row }">
            <ElCheckbox v-model="row.isInsert" true-label="1" />
          </template>
          <template #isEdit="{ row }">
            <ElCheckbox v-model="row.isEdit" true-label="1" />
          </template>
          <template #isList="{ row }">
            <ElCheckbox v-model="row.isList" true-label="1" />
          </template>
          <template #isQuery="{ row }">
            <ElCheckbox v-model="row.isQuery" true-label="1" />
          </template>
          <template #queryType="{ row }">
            <ElSelect v-model="row.queryType">
              <ElOption
                v-for="item in queryTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElSelect>
          </template>
          <template #isRequired="{ row }">
            <ElCheckbox v-model="row.isRequired" true-label="1" />
          </template>
          <template #htmlType="{ row }">
            <ElSelect v-model="row.htmlType">
              <ElOption
                v-for="item in htmlTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElSelect>
          </template>
          <template #dictType="{ row }">
            <ElSelect v-model="row.dictType">
              <ElOption
                v-for="item in dictsRef"
                :key="item.dictType"
                :label="item.dictName"
                :value="item.dictType"
              >
                <span class="float-left">{{ item.dictName }}</span>
                <span class="float-right text-gray-400">{{
                  item.dictType
                }}</span>
              </ElOption>
            </ElSelect>
          </template>
        </BasicTable>
        <div class="flex w-full justify-center">
          <ElButton type="primary" @click="genSubmit">提交</ElButton>
        </div>
      </ElTabPane>
      <ElTabPane label="生成信息" name="3" class="text-primary">
        <GenForm />
      </ElTabPane>
    </ElTabs>
  </Page>
</template>
