import type {
  Deployment,
  ExportQuery,
  FlowFormDataQuery,
  FlowTaskInfoQuery,
  MyProcessQuery,
  ProcessStartData,
  StopProcessData,
  TaskCompleteData,
  TaskRejectData,
  ReturnListData,
} from './model';

import type { BaseResult } from '#/api/base-result';

import { requestClient } from '#/api/request';

enum Api {
  myProcess = '/flowable/task/myProcess',
  flowFormData = '/flowable/task/flowFormData',
  flowTaskInfo = '/flowable/task/flowTaskInfo',
  complete = '/flowable/task/complete',
  stopProcess = '/flowable/task/stopProcess',
  reject = '/flowable/task/reject',
  returnList = '/flowable/task/returnList',
  startFlow = '/flowable/process/startFlow',
  deployment = '/system/deployment',
  exportDeployment = '/system/deployment/export',
}

// 我的发起的流程
export function myProcessList(params?: MyProcessQuery) {
  return requestClient.get<BaseResult<any[]>>(Api.myProcess, { params });
}

// 流程表单数据
export function flowFormData(params?: FlowFormDataQuery) {
  return requestClient.get<BaseResult<any>>(Api.flowFormData, { params });
}

// 流程任务信息
export function flowTaskInfo(params?: FlowTaskInfoQuery) {
  return requestClient.get<BaseResult<any>>(Api.flowTaskInfo, { params });
}

// 完成任务
export function complete(data: TaskCompleteData) {
  return requestClient.post<BaseResult<any>>(Api.complete, data, { successMessageMode: 'message' });
}

// 取消申请
export function stopProcess(data: StopProcessData) {
  return requestClient.post<BaseResult<any>>(Api.stopProcess, data, { successMessageMode: 'message' });
}

// 驳回任务
export function rejectTask(data: TaskRejectData) {
  return requestClient.post<BaseResult<any>>(Api.reject, data, { successMessageMode: 'message' });
}

// 可退回任务列表
export function returnList(data: ReturnListData) {
  return requestClient.post<BaseResult<any[]>>(Api.returnList, data);
}

// 部署流程实例
export function deployStart(deployId: string) {
  return requestClient.get<BaseResult<any>>(`${Api.startFlow}/${deployId}`);
}

// 查询流程定义详细
export function getDeployment(id: string) {
  return requestClient.get<BaseResult<Deployment>>(`${Api.deployment}/${id}`);
}

// 新增流程定义
export function addDeployment(data: Deployment) {
  return requestClient.post<BaseResult<any>>(Api.deployment, data, { successMessageMode: 'message' });
}

// 修改流程定义
export function updateDeployment(data: Deployment) {
  return requestClient.put<BaseResult<any>>(Api.deployment, data, { successMessageMode: 'message' });
}

// 删除流程定义
export function delDeployment(id: string) {
  return requestClient.delete<BaseResult<any>>(`${Api.deployment}/${id}`, { successMessageMode: 'message' });
}

// 导出流程定义
export function exportDeployment(params?: ExportQuery) {
  return requestClient.get<BaseResult<any>>(Api.exportDeployment, { params });
}