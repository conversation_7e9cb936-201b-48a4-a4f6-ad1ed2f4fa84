export interface Role {
  createBy?: any;
  createTime?: string;
  updateBy?: any;
  updateTime?: any;
  remark?: string;
  roleId: number;
  roleName?: string;
  roleKey?: string;
  roleSort?: number;
  dataScope?: string;
  menuCheckStrictly?: boolean;
  deptCheckStrictly?: boolean;
  status?: string;
  delFlag?: string;
  flag?: boolean;
  menuIds?: any;
  deptIds?: any;
  permissions?: any;
  admin?: boolean;
}

export interface DeptTree {
  id: number;
  label: string;
  disable: boolean;
  children: DeptTree[];
}

/**
 * @description 菜单返回
 * @param checkedKeys 选中的菜单id
 * @param menus 菜单信息
 */
export interface DeptResp {
  checkedKeys: number[];
  depts: DeptTree[];
}

export interface AuthRoleUserQuery {
  pageNum: number;
  pageSize: number;
  roleId: number;
  userName: string;
  phonenumber: string;
}
