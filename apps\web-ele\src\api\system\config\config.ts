import type { ConfigModel } from './model';

import type { BaseResult } from '#/api/base-result';
import type { DictData } from '#/api/system/dict/dict-data-model';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  configList = '/system/config/list',
  exportConfig = '/system/config/export',
  refresh = '/system/config/refreshCache',
  root = '/system/config',
}
// 获取参数配置列表
export function configList(params: any) {
  return requestClient.get<BaseResult<ConfigModel[]>>(Api.configList, {
    params,
  });
}
// 获取配置详情
export function configDetail(configId: number) {
  return requestClient.get<ConfigModel>(`${Api.root}/${configId}`);
}
// 删除参数列表
export function deleteConfig(configIds: number[]) {
  return requestClient.delete(`${Api.root}/${configIds.join(',')}`, {
    successMessageMode: 'message',
  });
}
// 新增
export function addConfig(config: ConfigModel) {
  return requestClient.post(Api.root, config, {
    successMessageMode: 'message',
  });
}
// 编辑
export function updateConfig(config: ConfigModel) {
  return requestClient.put(Api.root, config, { successMessageMode: 'message' });
}
// 刷新
export function refreshConfig() {
  return requestClient.delete(Api.refresh, { successMessageMode: 'message' });
}
// 导出
export function exportConfig(data: Partial<DictData>) {
  return commonExport(Api.exportConfig, data);
}
