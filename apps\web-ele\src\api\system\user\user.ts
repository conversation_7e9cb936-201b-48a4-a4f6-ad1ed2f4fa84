import type {
  File<PERSON>allBack,
  Pwd,
  User,
  UserImportParam,
  UserQuery,
} from './model';

import type {
  BaseDataResult,
  BaseProfileResult,
  BaseResult,
} from '#/api/base-result';

import { buildUUID } from '@vben/utils';

import { commonExport, ContentTypeEnum } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  baseUser = '/system/user',
  downLoadTemplate = '/system/user/importTemplate',
  exportUser = '/system/user/export',
  menuList = '/system/user/list',
  modifyPwd = '/system/user/resetPwd',
  updateAvatar = '/system/user/profile/avatar',
  updateProfilePwd = '/system/user/profile/updatePwd',
  updateUser = '/system/user/changeStatus',
  userImport = '/system/user/importData',
  userInfo = 'system/user/profile',
}

// 用户列表接口
export function getUserList(params?: UserQuery) {
  return requestClient.get<BaseResult<User[]>>(Api.menuList, { params });
}
// 下载导入模板
export function downLoadTemplate() {
  return requestClient.post<Blob>(
    Api.downLoadTemplate,
    {},
    {
      isTransformResponse: false,
      responseType: 'blob',
    },
  );
}
// 导出用户
export function userExport(data: Partial<User>) {
  return commonExport(Api.exportUser, data);
}
// 导入用户
export function userImportData(data: UserImportParam) {
  return requestClient.post<{ code: number; msg: string }>(
    Api.userImport,
    data,
    {
      headers: {
        'Content-Type': ContentTypeEnum.FORM_DATA,
      },
      isTransformResponse: false,
    },
  );
}

// 获取单个用户信息
export function getUserOne(id: number) {
  return requestClient.get<BaseDataResult<User>>(`${Api.baseUser}/${id}`, {
    isTransformResponse: false,
  });
}
// 更改用户状态
export function updateUserStatus(user: User) {
  return requestClient.put<BaseResult<any>>(Api.updateUser, user, {
    successMessageMode: 'message',
  });
}
// 更改用户全部信息
export function updateUser(user: User) {
  return requestClient.put<BaseResult<any>>(Api.baseUser, user, {
    successMessageMode: 'message',
  });
}
// 新增用户
export function addUser(user: User) {
  return requestClient.post(Api.baseUser, user, {
    successMessageMode: 'message',
  });
}
// 批量删除用户
export function deleteUsers(ids: number[]) {
  return requestClient.delete(`${Api.baseUser}/${ids}`, {
    successMessageMode: 'message',
  });
}
// 修改用户密码
export function modifyUserPwd(user: User) {
  return requestClient.put(Api.modifyPwd, user, {
    successMessageMode: 'message',
  });
}
// 个人中心用户信息
export function userProfile() {
  return requestClient.get<BaseProfileResult<User>>(Api.userInfo, {
    isTransformResponse: false,
  });
}
// 个人中心修改信息
export function updateProfile(user: User) {
  return requestClient.put(Api.userInfo, user, {
    successMessageMode: 'message',
  });
}
// 个人中心修改密码
export function updateProfilePwd(pwd: Pwd) {
  return requestClient.put(Api.updateProfilePwd, pwd, {
    successMessageMode: 'message',
  });
}
/**
 * 用户更新个人头像
 * @param fileCallback data
 * @returns void
 */
export function userUpdateAvatar(fileCallback: FileCallBack) {
  /** 直接点击头像上传 filename为空 由于后台通过拓展名判断(默认文件名blob) 会上传失败 */
  let { file } = fileCallback;
  const { filename } = fileCallback;
  /**
   * Blob转File类型
   * 1. 在直接点击确认 filename为空 取uuid作为文件名
   * 2. 选择上传必须转为File类型 Blob类型上传后台获取文件名为空
   */
  file = filename
    ? new File([file], filename)
    : new File([file], `${buildUUID()}.png`);
  return requestClient.post(
    Api.updateAvatar,
    {
      avatarfile: file,
    },
    { headers: { 'Content-Type': 'multipart/form-data' } },
  );
}
