import type { FormSchemaGetter } from '#/adapter/form';

import { z } from '#/adapter/form';

const sexData = [
  {
    label: '男',
    value: '0',
  },
  {
    label: '女',
    value: '1',
  },
];
export const userInfoSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    label: '用户昵称',
    fieldName: 'nickName',
    componentProps: {
      maxlength: 30,
    },
    rules: 'required',
  },
  {
    component: 'Input',
    label: '手机号码',
    componentProps: {
      maxlength: 11,
    },
    fieldName: 'phonenumber',
    rules: z
      .string()
      .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
      .refine((val) => val.length === 11, '必须是11位数字'),
  },
  {
    component: 'Input',
    label: '邮箱',
    componentProps: {
      maxlength: 50,
    },
    fieldName: 'email',
    rules: z
      .string()
      .trim()
      .refine(
        (value) =>
          // 允许空字符串或符合邮箱格式
          value === '' || z.string().email().safeParse(value).success,
        { message: '请输入有效的邮箱地址' },
      ),
  },
  {
    component: 'RadioGroup',
    label: '性别',
    componentProps: {
      isButton: true,
      options: sexData,
    },
    defaultValue: '0',
    fieldName: 'sex',
  },
];

export const pwdSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    label: '旧密码',
    fieldName: 'oldPassword',
    componentProps: {
      maxlength: 20,
      type: 'password',
      showPassword: true,
    },
    rules: 'required',
  },
  {
    component: 'Input',
    label: '新密码',
    componentProps: {
      maxlength: 20,
      type: 'password',
      showPassword: true,
    },
    dependencies: {
      rules(values) {
        return z
          .string({ message: '请输入新密码' })
          .min(6, '密码长度不能少于6个字符')
          .max(20, '密码长度不能超过20个字符')
          .refine((value) => value !== values.oldPassword, '新旧密码不能相同');
      },
      triggerFields: ['newPassword', 'oldPassword'],
    },
    fieldName: 'newPassword',
    rules: 'required',
  },
  {
    component: 'Input',
    label: '确认密码',
    componentProps: {
      maxlength: 20,
      type: 'password',
      showPassword: true,
    },
    fieldName: 'confirmPassword',
    dependencies: {
      rules(values) {
        return z
          .string({ message: '请输入确认密码' })
          .min(6, '密码长度不能少于6个字符')
          .max(20, '密码长度不能超过20个字符')
          .refine(
            (value) => value === values.newPassword,
            '新密码和确认密码不一致',
          );
      },
      triggerFields: ['newPassword', 'confirmPassword'],
    },
    rules: 'required',
  },
];
