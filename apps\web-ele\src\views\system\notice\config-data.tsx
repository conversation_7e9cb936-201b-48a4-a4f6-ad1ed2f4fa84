import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';

import { getDictOptions } from '#/utils/dict';

export const queryFormSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'noticeTitle',
    label: '公告标题',
  },
  {
    component: 'Input',
    fieldName: 'createBy',
    label: '操作人员',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_NOTICE_TYPE),
    },
    fieldName: 'noticeType',
    label: '类型',
  },
];

export const tableColumns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 80,
  },
  {
    field: 'noticeId',
    title: '序号',
    width: 80,
  },
  {
    field: 'noticeTitle',
    title: '公告标题',
    minWidth: 180,
  },
  {
    field: 'noticeType',
    slots: { default: 'noticeType' },
    title: '公告类型',
  },
  {
    field: 'status',
    slots: { default: 'status' },
    title: '状态',
  },
  {
    field: 'createBy',
    title: '创建者',
  },
  {
    field: 'createTime',
    title: '创建时间',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
  },
];

export const modalFormSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'noticeId',
  },
  {
    component: 'Input',
    fieldName: 'noticeTitle',
    label: '公告标题',
    componentProps: {
      maxlength: 100,
    },
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
      isButton: true,
    },
    fieldName: 'status',
    defaultValue: '0',
    label: '公告状态',
    formItemClass: 'col-span-1',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_NOTICE_TYPE),
      isButton: true,
    },
    fieldName: 'noticeType',
    defaultValue: '1',
    label: '公告类型',
    formItemClass: 'col-span-1',
  },
  {
    component: 'RichTextarea',
    componentProps: {
      width: '100%',
      showImageUpload: false,
    },
    fieldName: 'noticeContent',
    label: '公告内容',
    rules: 'required',
  },
];
