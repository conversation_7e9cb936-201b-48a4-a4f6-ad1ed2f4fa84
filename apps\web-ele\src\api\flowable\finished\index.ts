import type {
  Deployment,
  ExportQuery,
  FinishedQuery,
} from './model';

import type { BaseResult } from '#/api/base-result';

import { requestClient } from '#/api/request';

enum Api {
  finishedList = '/flowable/task/finishedList',
  flowRecord = '/flowable/task/flowRecord',
  startFlow = '/flowable/process/startFlow',
  deployment = '/system/deployment',
  deleteInstance = '/flowable/instance/delete',
  exportDeployment = '/system/deployment/export',
}

// 查询已办任务列表
export function finishedList(params?: FinishedQuery) {
  return requestClient.get<BaseResult<any[]>>(Api.finishedList, { params });
}

// 任务流转记录
export function flowRecord(params?: { procInsId?: string }) {
  return requestClient.get<BaseResult<any[]>>(Api.flowRecord, { params });
}

// 部署流程实例
export function deployStart(deployId: string) {
  return requestClient.get<BaseResult<any>>(`${Api.startFlow}/${deployId}`);
}

// 查询流程定义详细
export function getDeployment(id: string) {
  return requestClient.get<BaseResult<Deployment>>(`${Api.deployment}/${id}`);
}

// 新增流程定义
export function addDeployment(data: Deployment) {
  return requestClient.post<BaseResult<any>>(Api.deployment, data, { successMessageMode: 'message' });
}

// 修改流程定义
export function updateDeployment(data: Deployment) {
  return requestClient.put<BaseResult<any>>(Api.deployment, data, { successMessageMode: 'message' });
}

// 删除流程定义
export function delDeployment(id: string) {
  return requestClient.delete<BaseResult<any>>(`${Api.deleteInstance}/${id}`, { successMessageMode: 'message' });
}

// 导出流程定义
export function exportDeployment(params?: ExportQuery) {
  return requestClient.get<BaseResult<any>>(Api.exportDeployment, { params });
}