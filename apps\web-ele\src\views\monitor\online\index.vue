<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { OnLine } from '#/api/monitor/online/model';

import { Page } from '@vben/common-ui';

import dayjs from 'dayjs';
import { ElMessageBox } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { forceUser, onLineList } from '#/api/monitor/online/online';

import { querySchema, tableColumns } from './config-data';

const formOptions: VbenFormProps = {
  schema: querySchema(),
  wrapperClass: 'grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
  },
  columns: tableColumns,
  size: 'medium',
  height: 'auto',
  // 配置表格右上角全屏、刷新
  toolbarConfig: {
    custom: true,
    refresh: true,
    zoom: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        const resp = await onLineList({
          ...formValues,
          pageNum: page.currentPage,
          pageSize: page.pageSize,
        });
        return { items: resp.rows, total: resp.total };
      },
    },
  },
};
const [BasicTable, basicTableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
async function forceExit(onLine: OnLine) {
  ElMessageBox.confirm(
    `确认要强制退出账号为${onLine.userName}的用户吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(async () => {
    await forceUser(onLine.tokenId);
    await basicTableApi.reload();
  });
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="在线用户">
      <template #loginTime="{ row }">
        {{
          row.loginTime
            ? dayjs(row.loginTime).format('YYYY-MM-DD HH:mm:ss')
            : ''
        }}
      </template>
      <template #action="{ row }">
        <ElSpace>
          <ElButton
            size="small"
            type="primary"
            plain
            @click="forceExit(row)"
            v-access:code="['monitor:online:forceLogout']"
          >
            强退
          </ElButton>
        </ElSpace>
      </template>
    </BasicTable>
  </Page>
</template>
