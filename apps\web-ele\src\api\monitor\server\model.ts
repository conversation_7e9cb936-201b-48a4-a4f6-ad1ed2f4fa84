interface Cpu {
  cpuNum?: number;
  total?: number;
  sys?: number;
  used?: number;
  wait?: number;
  free?: number;
}
interface Jvm {
  total?: number;
  max?: number;
  free?: number;
  version?: number;
  home?: number;
  used?: number;
  startTime?: string;
  runTime?: string;
  inputArgs?: string;
  usage?: number;
  name?: string;
}
interface Mem {
  total?: number;
  used?: number;
  free?: number;
  usage?: number;
}
interface Sys {
  computerName?: string;
  computerIp?: string;
  userDir?: string;
  osName?: string;
  osArch?: string;
}
interface SysFile {
  dirName?: string;
  sysTypeName?: string;
  typeName?: string;
  total?: string;
  free?: string;
  used?: string;
  usage?: number;
}
export interface Server {
  cpu?: Cpu;
  jvm?: Jvm;
  mem?: Mem;
  sys?: Sys;
  sysFiles?: SysFile[];
}
