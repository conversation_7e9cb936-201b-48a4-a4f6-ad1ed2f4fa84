<script setup lang="ts">
import type { Operlog } from '#/api/monitor/operlog/model';
import type { DictOption } from '#/store/dict';

import { nextTick, onMounted, ref } from 'vue';

import { EllipsisText, JsonViewer, useVbenDrawer } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';

import { DictTag } from '#/components/dict';
import { getDictOptions } from '#/utils/dict';

// 获取状态字典值
const dictRef = ref<DictOption[]>([]);
// 获取操作类型
const operTypeRef = ref<DictOption[]>([]);
onMounted(() => {
  dictRef.value = getDictOptions(DictEnum.SYS_COMMON_STATUS);
  operTypeRef.value = getDictOptions(DictEnum.SYS_OPER_TYPE);
});
// 打开侧拉传递过来的实体类
const dataRef = ref<Operlog>({ operId: -1, businessType: -1 });
const [Drawer, drawerApi] = useVbenDrawer({
  showConfirmButton: false,
  showCancelButton: false,
  onOpenChange(isOpen) {
    if (!isOpen) {
      return;
    }
    nextTick(() => {
      dataRef.value = drawerApi.getData<Operlog>();
    });
  },
});
</script>

<template>
  <Drawer title="详情" class="w-[600px]">
    <ElDescriptions border column="1">
      <ElDescriptionsItem label="系统模块">
        {{ dataRef.title }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="操作类型">
        <DictTag :dicts="operTypeRef" :value="dataRef.businessType" />
      </ElDescriptionsItem>
      <ElDescriptionsItem label="请求地址">
        {{ dataRef.operUrl }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="操作人员">
        {{ dataRef.operName }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="操作地址">
        {{ dataRef.operIp }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="操作地点">
        {{ dataRef.operLocation }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="操作方法">
        {{ dataRef.method }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="请求参数">
        <JsonViewer
          :value="dataRef.operParam"
          :expand-depth="3"
          copyable
          preview-mode
          :show-double-quotes="false"
          :show-array-index="false"
        />
      </ElDescriptionsItem>
      <ElDescriptionsItem label="返回参数">
        <JsonViewer
          :value="dataRef.jsonResult"
          :expand-depth="3"
          copyable
          preview-mode
          :show-double-quotes="false"
          :show-array-index="false"
        />
      </ElDescriptionsItem>
      <ElDescriptionsItem label="操作状态">
        <DictTag :dicts="dictRef" :value="dataRef?.status ?? -1" />
      </ElDescriptionsItem>
      <ElDescriptionsItem label="消耗时间">
        {{ `${dataRef.costTime}毫秒` }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="操作时间">
        {{ dataRef.operTime }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="异常信息" v-if="dataRef.status === 1">
        <EllipsisText :line="3" expand> {{ dataRef.errorMsg }}</EllipsisText>
      </ElDescriptionsItem>
    </ElDescriptions>
  </Drawer>
</template>
<style scoped lang="scss">
:deep(.is-bordered-label) {
  min-width: 100px !important;
  font-weight: normal !important;
}
</style>
