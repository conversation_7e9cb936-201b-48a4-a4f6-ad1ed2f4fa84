import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';

import { dictTypeOptions } from '#/api/system/dict/dict-data';
import { getDictOptions } from '#/utils/dict';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'ApiSelect',
    fieldName: 'dictType',
    label: '字典名称',
    componentProps: {
      api: async () => {
        return await dictTypeOptions();
      },
      labelField: 'dictName',
      valueField: 'dictType',
    },
  },
  {
    component: 'Input',
    fieldName: 'dictLabel',
    label: '字典标签',
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '状态',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
    },
  },
];
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    title: '字典编码',
    field: 'dictCode',
  },
  {
    title: '字典标签',
    field: 'dictLabel',
    slots: { default: 'dictLabel' },
  },
  {
    title: '字典键值',
    field: 'dictValue',
  },
  {
    title: '状态',
    field: 'status',
    slots: { default: 'status' },
  },
  {
    title: '创建时间',
    field: 'createTime',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    resizable: false,
    title: '操作',
    width: 180,
  },
];
const showStyleOptions = [
  {
    label: '默认(default)',
    value: 'default',
  },
  {
    label: '主要(primary)',
    value: 'primary',
  },
  {
    label: '成功(success)',
    value: 'success',
  },
  {
    label: '信息(info)',
    value: 'info',
  },
  {
    label: '警告(warning)',
    value: 'warning',
  },
  {
    label: '危险(danger)',
    value: 'danger',
  },
];
export const drawerFormSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'dictCode',
    dependencies: {
      show: false,
      triggerFields: [''],
    },
  },
  {
    component: 'Input',
    fieldName: 'dictType',
    disabled: true,
    label: '字典类型',
  },
  {
    component: 'Input',
    fieldName: 'dictLabel',
    label: '字典标签',
    rules: 'required',
    componentProps: {
      maxlength: 100,
    },
  },
  {
    component: 'Input',
    fieldName: 'dictValue',
    label: '字典键值',
    rules: 'required',
    componentProps: {
      maxlength: 100,
    },
  },
  {
    component: 'Input',
    fieldName: 'cssClass',
    label: '样式属性',
    componentProps: {
      maxlength: 100,
    },
  },
  {
    component: 'InputNumber',
    fieldName: 'dictSort',
    label: '显示排序',
    componentProps: {
      max: 9999,
    },
    defaultValue: 0,
    rules: 'required',
  },
  {
    component: 'Select',
    fieldName: 'listClass',
    label: '回显样式',
    componentProps: {
      options: showStyleOptions,
      maxlength: 100,
    },
    defaultValue: 'default',
  },
  {
    component: 'RadioGroup',
    fieldName: 'status',
    label: '状态',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
      isButton: true,
    },
    defaultValue: '0',
  },
  {
    component: 'Input',
    fieldName: 'remark',
    label: '备注',
    componentProps: {
      type: 'textarea',
      maxlength: 500,
      showWordLimit: true,
    },
  },
];
