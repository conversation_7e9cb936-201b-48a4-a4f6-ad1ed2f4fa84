import type { Role } from '#/api/system/role/model';

export interface UserImportParam {
  updateSupport: boolean;
  file: Blob | File;
}
interface Dept {
  createBy?: any;
  createTime?: any;
  updateBy?: any;
  updateTime?: any;
  remark?: any;
  deptId?: number;
  parentId?: any;
  ancestors?: any;
  deptName?: string;
  orderNum?: any;
  leader?: string;
  phone?: any;
  email?: any;
  status?: any;
  delFlag?: any;
  parentName?: any;
}
export interface User {
  createBy?: string;
  createTime?: string;
  updateBy?: any;
  updateTime?: any;
  remark?: string;
  userId: number;
  deptId?: number;
  deptName?: string;
  userName?: string;
  nickName?: string;
  email?: string;
  phonenumber?: string;
  sex?: string;
  avatar?: string;
  password?: any;
  status: string;
  delFlag?: string;
  loginIp?: string;
  loginDate?: string;
  dept?: Dept;
  roleIds?: any;
  postIds?: any;
  roleId?: any;
  admin?: boolean;
  roles?: Role[];
}

interface Params {
  beginTime?: string;
  endTime?: string;
}
export interface UserQuery {
  userName?: string;
  phonenumber?: string;
  status?: string;
  params?: Params;
  pageNum: 1;
  pageSize: 10;
  deptId?: number;
}
export interface Pwd {
  oldPassword: string;
  newPassword: string;
}
export interface FileCallBack {
  name: string;
  file: Blob;
  filename: string;
}
