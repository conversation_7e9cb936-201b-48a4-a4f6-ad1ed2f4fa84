import type { BaseResult } from '#/api/base-result';
import type { CodeTemp, Gen, TableDetail } from '#/api/tool/gen/model';

import { requestClient } from '#/api/request';

enum Api {
  baseApi = '/tool/gen',
  createTable = '/tool/gen/createTable',
  dbList = '/tool/gen/db/list',
  genCode = '/tool/gen/batchGenCode',
  genList = '/tool/gen/list',
  importDb = '/tool/gen/importTable',
  preview = '/tool/gen/preview',
  syncTable = '/tool/gen/synchDb',
}
// 获取列表
export function getGenList(params?: any) {
  return requestClient.get<BaseResult<Gen[]>>(Api.genList, { params });
}
// 获取表详情
export function detailTable(tableId: string) {
  return requestClient.get<TableDetail>(`${Api.baseApi}/${tableId}`);
}
// 导入弹窗获取表列表
export function dbList(params?: any) {
  return requestClient.get<BaseResult<Gen[]>>(Api.dbList, { params });
}
// 导入表
export function importDb(tables: string[]) {
  return requestClient.post(`${Api.importDb}?tables=${tables.join(',')}`, {
    successMessageMode: 'message',
  });
}
// 删除列表数据
export function deleteGen(tableIds: number[]) {
  return requestClient.delete(`${Api.baseApi}/${tableIds.join(',')}`, {
    successMessageMode: 'message',
  });
}
// 代码预览
export function codePreview(tableId: number) {
  return requestClient.get<CodeTemp>(`${Api.preview}/${tableId}`);
}
// 同步表结构
export function syncTable(tableName: string) {
  return requestClient.get(`${Api.syncTable}/${tableName}`, {
    successMessageMode: 'message',
  });
}
// 导出生成代码
export function genCode(tables: string[]) {
  return requestClient.get<Blob>(`${Api.genCode}?tables=${tables.join(',')}`, {
    isTransformResponse: false,
    responseType: 'blob',
  });
}
// sql生成表
export function createTable(sql: string) {
  return requestClient.post(
    `${Api.createTable}?sql=${sql}`,
    {},
    { successMessageMode: 'message' },
  );
}
// 编辑
export function updateGen(gen: Gen) {
  return requestClient.put(Api.baseApi, gen, { successMessageMode: 'message' });
}
