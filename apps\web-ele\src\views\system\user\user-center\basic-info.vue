<script setup lang="ts">
import type { User } from '#/api/system/user/model';

import { computed, defineEmits, defineProps } from 'vue';

import { useAppConfig } from '@vben/hooks';
import { preferences } from '@vben/preferences';

import { isEmpty, isNull } from 'lodash-es';

import { userUpdateAvatar } from '#/api/system/user/user';
import { CropperAvatar } from '#/components/cropper';

const props = defineProps<{ post?: string; user: User }>();

defineEmits<{
  // 头像上传完毕
  uploadFinish: [];
}>();

const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);

const avatar = computed(() => {
  if (isEmpty(props.user.avatar) || isNull(props.user.avatar)) {
    return preferences.app.defaultAvatar;
  } else if (props.user.avatar?.includes('http')) {
    return props.user.avatar;
  } else {
    return apiURL + props.user.avatar;
  }
});
</script>

<template>
  <ElCard class="h-full lg:w-1/3">
    <div class="flex w-full flex-col items-center">
      <div class="flex flex-col items-center gap-[24px]">
        <ElTooltip content="点击上传头像" placement="top">
          <CropperAvatar
            :show-btn="false"
            :upload-api="userUpdateAvatar"
            :value="avatar"
            width="120"
            @change="$emit('uploadFinish')"
          />
        </ElTooltip>
        <div class="text-foreground my-4 text-xl font-bold">
          {{ props.user.nickName }}
        </div>
      </div>
      <ElDescriptions
        :column="1"
        direction="horizontal"
        label-align="left"
        class="w-full"
      >
        <ElDescriptionsItem label="用户账号:">
          {{ props.user.userName }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="手机号码:">
          {{ props.user.phonenumber }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="用户邮箱:">
          {{ props.user.email }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="所属部门:">
          {{ props.user?.dept?.deptName }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="所属岗位:">
          {{ props.post }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="所属角色:">
          <ElTag
            v-for="item in props.user.roles"
            :key="item.roleId"
            class="mr-2"
          >
            {{ item.roleName }}
          </ElTag>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="创建日期:">
          {{ props.user.createTime }}
        </ElDescriptionsItem>
      </ElDescriptions>
    </div>
  </ElCard>
</template>
