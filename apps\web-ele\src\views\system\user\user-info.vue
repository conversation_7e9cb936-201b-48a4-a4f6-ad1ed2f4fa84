<script lang="ts" setup>
import type { User } from '#/api/system/user/model';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import dayjs from 'dayjs';

const userRef = ref<User>({ userId: -1, status: '0' });
const [Modal, modalApi] = useVbenModal({
  onOpenChange() {
    userRef.value = modalApi.getData<User>();
  },
});
</script>
<template>
  <Modal title="用户信息">
    <ElDescriptions border column="1">
      <ElDescriptionsItem label="用户编号" width="80">
        {{ userRef.userId }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="用户昵称" width="80">
        {{ userRef.nickName }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="用户状态" width="80">
        <ElTag :type="userRef.status === '0' ? 'primary' : 'danger'">
          {{ userRef.status === '0' ? '正常' : '停用' }}
        </ElTag>
      </ElDescriptionsItem>
      <ElDescriptionsItem label="所属部门" width="80">
        {{ userRef.deptName }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="手机号" width="80">
        {{ userRef.phonenumber }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="邮箱" width="80">
        {{ userRef.email }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="创建时间" width="80">
        {{ userRef.createTime }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="上次登录IP" width="80">
        {{ userRef.loginIp }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="上次登录时间" width="80">
        {{
          userRef.loginDate
            ? dayjs(userRef.loginDate).format('YYYY-MM-DD HH:mm:ss')
            : ''
        }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="备注" width="80">
        {{ userRef.remark }}
      </ElDescriptionsItem>
    </ElDescriptions>
  </Modal>
</template>
<style scoped lang="scss">
:deep(.is-bordered-label) {
  font-weight: normal !important;
}
</style>
